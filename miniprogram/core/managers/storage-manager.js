/**
 * 存储管理器
 * 负责数据的存储、压缩、序列化和反序列化
 *
 * 功能特性：
 * - 数据压缩/解压，优化存储空间
 * - 数据序列化/反序列化
 * - 统一的存储接口
 * - 错误处理和数据恢复
 */

/**
 * 简化的Deflate压缩算法实现
 * 结合LZ77和简单的位编码，适合文本数据压缩
 */
class DeflateCompressor {
  /**
   * 压缩数据
   * @param {string} data - 要压缩的字符串
   * @returns {string} 压缩后的字符串
   */
  static compress(data) {
    if (!data || data.length === 0) {
      return ''
    }

    // 第一步：LZ77压缩 - 查找重复字符串
    const lz77Compressed = this.lz77Compress(data)

    // 第二步：简单的频率编码
    const frequencyEncoded = this.frequencyEncode(lz77Compressed)

    return frequencyEncoded
  }

  /**
   * 解压数据
   * @param {string} compressed - 压缩后的字符串
   * @returns {string} 解压后的字符串
   */
  static decompress(compressed) {
    if (!compressed || compressed.length === 0) {
      return ''
    }

    try {
      // 第一步：频率解码
      const frequencyDecoded = this.frequencyDecode(compressed)

      // 第二步：LZ77解压
      const lz77Decompressed = this.lz77Decompress(frequencyDecoded)

      return lz77Decompressed
    } catch (error) {
      throw new Error('Deflate解压失败: ' + error.message)
    }
  }

  /**
   * LZ77压缩 - 查找重复字符串并用引用替换
   * @param {string} data - 输入数据
   * @returns {string} LZ77压缩后的数据
   */
  static lz77Compress(data) {
    const result = []
    let i = 0
    const windowSize = 4096  // 滑动窗口大小
    const maxMatchLength = 258  // 最大匹配长度

    while (i < data.length) {
      let bestMatch = { length: 0, distance: 0 }

      // 在滑动窗口中查找最长匹配
      const windowStart = Math.max(0, i - windowSize)

      for (let j = windowStart; j < i; j++) {
        let matchLength = 0

        // 计算匹配长度
        while (matchLength < maxMatchLength &&
               i + matchLength < data.length &&
               data[j + matchLength] === data[i + matchLength]) {
          matchLength++
        }

        // 更新最佳匹配
        if (matchLength > bestMatch.length && matchLength >= 3) {
          bestMatch = {
            length: matchLength,
            distance: i - j
          }
        }
      }

      if (bestMatch.length > 0) {
        // 找到匹配，输出引用
        result.push(`<${bestMatch.distance},${bestMatch.length}>`)
        i += bestMatch.length
      } else {
        // 没有匹配，输出字符
        result.push(data[i])
        i++
      }
    }

    return result.join('')
  }

  /**
   * LZ77解压
   * @param {string} compressed - LZ77压缩的数据
   * @returns {string} 解压后的数据
   */
  static lz77Decompress(compressed) {
    const result = []
    let i = 0

    while (i < compressed.length) {
      if (compressed[i] === '<') {
        // 解析引用
        const endIndex = compressed.indexOf('>', i)
        if (endIndex === -1) {
          throw new Error('无效的LZ77格式')
        }

        const reference = compressed.substring(i + 1, endIndex)
        const [distance, length] = reference.split(',').map(Number)

        // 复制引用的数据
        const startPos = result.length - distance
        for (let j = 0; j < length; j++) {
          result.push(result[startPos + j])
        }

        i = endIndex + 1
      } else {
        // 直接字符
        result.push(compressed[i])
        i++
      }
    }

    return result.join('')
  }

  /**
   * 频率编码 - 简化的Huffman编码思想
   * @param {string} data - 输入数据
   * @returns {string} 频率编码后的数据
   */
  static frequencyEncode(data) {
    // 统计字符频率
    const frequency = new Map()
    for (const char of data) {
      frequency.set(char, (frequency.get(char) || 0) + 1)
    }

    // 创建简单的编码表（高频字符用短编码）
    const sortedChars = Array.from(frequency.entries())
      .sort((a, b) => b[1] - a[1])

    const codeTable = new Map()
    const decodeTable = new Map()

    // 为最常见的字符分配短编码
    for (let i = 0; i < sortedChars.length; i++) {
      const char = sortedChars[i][0]
      let code

      if (i < 16) {
        // 前16个字符用单字节编码
        code = String.fromCharCode(0x80 + i)
      } else {
        // 其他字符保持原样
        code = char
      }

      codeTable.set(char, code)
      decodeTable.set(code, char)
    }

    // 编码数据
    let encoded = ''
    for (const char of data) {
      encoded += codeTable.get(char)
    }

    // 将解码表序列化并添加到开头
    const tableData = JSON.stringify(Array.from(decodeTable.entries()))
    return tableData.length.toString().padStart(8, '0') + tableData + encoded
  }

  /**
   * 频率解码
   * @param {string} encoded - 频率编码的数据
   * @returns {string} 解码后的数据
   */
  static frequencyDecode(encoded) {
    // 读取解码表长度
    const tableLength = parseInt(encoded.substring(0, 8))

    // 读取解码表
    const tableData = encoded.substring(8, 8 + tableLength)
    const decodeTable = new Map(JSON.parse(tableData))

    // 解码数据
    const encodedData = encoded.substring(8 + tableLength)
    let decoded = ''

    for (const char of encodedData) {
      decoded += decodeTable.get(char) || char
    }

    return decoded
  }
}

/**
 * Deflate数据压缩器
 * 使用简化的Deflate算法进行无损压缩
 */
class DeflateDataCompressor {
  /**
   * 压缩数据
   * @param {string} data - 要压缩的字符串
   * @returns {string} 压缩后的字符串
   */
  static compress(data) {
    try {
      console.log(`开始Deflate压缩，原始大小: ${data.length} 字符`)

      // 使用Deflate压缩
      const compressed = DeflateCompressor.compress(data)

      console.log(`Deflate压缩后大小: ${compressed.length} 字符`)

      const compressionRatio = ((data.length - compressed.length) / data.length * 100).toFixed(2)
      console.log(`压缩率: ${compressionRatio}%`)

      // 添加Deflate标识，一律使用压缩数据
      return '~DEFLATE~' + compressed

    } catch (error) {
      console.warn('Deflate压缩失败，使用原始数据:', error)
      return data
    }
  }

  /**
   * 解压数据
   * @param {string} data - 可能被压缩的字符串
   * @returns {string} 解压后的字符串
   */
  static decompress(data) {
    try {
      // 检查Deflate压缩标识
      const deflateMatch = data.match(/^~DEFLATE~(.*)$/)

      if (deflateMatch) {
        // Deflate解压
        return DeflateCompressor.decompress(deflateMatch[1])
      } else {
        // 未压缩的数据
        return data
      }

    } catch (error) {
      console.warn('Deflate解压失败，返回原始数据:', error)
      return data
    }
  }


}

const { getStorageKey } = require('../../config/index')

/**
 * 存储管理器类
 * 负责所有数据的存储操作
 */
export class StorageManager {
  constructor() {
    // 存储键名 - 使用配置文件
    this.storageKey = getStorageKey('USER_DATA') || 'USER_DATA'
  }

  /**
   * 从存储加载数据（同步版本）
   * @returns {Object|null} 加载的数据对象，失败时返回null
   */
  loadDataSync() {
    try {
      console.log('同步加载存储数据...')

      const compressedData = wx.getStorageSync(this.storageKey)

      if (!compressedData) {
        console.log('未找到存储数据')
        return null
      }

      // 验证数据格式
      if (typeof compressedData !== 'string') {
        console.error('存储数据格式无效，期望字符串，实际:', typeof compressedData)
        return null
      }

      // 尝试解压数据
      let jsonData
      try {
        jsonData = DeflateDataCompressor.decompress(compressedData)
      } catch (decompressError) {
        console.warn('数据解压失败，尝试直接解析:', decompressError.message)
        jsonData = compressedData
      }

      // 验证JSON格式
      let loadedData
      try {
        loadedData = JSON.parse(jsonData)
      } catch (parseError) {
        console.error('JSON解析失败:', parseError.message)
        return null
      }

      console.log('同步加载数据成功')
      return loadedData

    } catch (error) {
      console.error('同步加载数据失败:', error)
      return null
    }
  }

  /**
   * 从存储加载数据
   * @returns {Object|null} 加载的数据对象，失败时返回null
   */
  async loadData() {
    try {
      console.log('开始从存储加载数据...')

      const compressedData = wx.getStorageSync(this.storageKey)

      if (!compressedData) {
        console.log('未找到存储数据')
        return null
      }

      // 验证数据格式
      if (typeof compressedData !== 'string') {
        console.error('存储数据格式无效，期望字符串，实际:', typeof compressedData)
        return null
      }

      // 尝试解压数据
      let jsonData
      try {
        jsonData = DeflateDataCompressor.decompress(compressedData)
      } catch (decompressError) {
        console.warn('数据解压失败，尝试直接解析:', decompressError.message)
        jsonData = compressedData
      }

      // 验证JSON格式
      let loadedData
      try {
        loadedData = JSON.parse(jsonData)
      } catch (parseError) {
        console.error('JSON解析失败:', parseError.message)
        console.error('原始数据长度:', jsonData.length)
        return null
      }

      // 验证数据结构
      if (!loadedData || typeof loadedData !== 'object') {
        console.error('加载的数据结构无效')
        return null
      }

      console.log('数据加载成功，版本:', loadedData.version)
      return loadedData

    } catch (error) {
      console.error('加载数据失败:', error.message)
      console.error('错误堆栈:', error.stack)
      return null
    }
  }

  /**
   * 保存数据到存储
   * @param {Object} data - 要保存的数据对象
   * @returns {Promise<boolean>} 保存是否成功
   */
  saveData(data) {
    try {
      console.log('开始保存数据到存储...')

      // 验证输入数据
      if (!data || typeof data !== 'object') {
        console.error('保存数据无效，期望对象，实际:', typeof data)
        return false
      }

      // 准备保存的数据，将Date对象转换为字符串
      let dataToSave
      try {
        dataToSave = this.prepareDataForSave(data)
      } catch (prepareError) {
        console.error('准备保存数据失败:', prepareError.message)
        return false
      }

      // 序列化数据
      let jsonData
      try {
        jsonData = JSON.stringify(dataToSave)
      } catch (stringifyError) {
        console.error('数据序列化失败:', stringifyError.message)
        return false
      }

      // 检查数据大小 - 使用配置文件中的限制
      const { Constants } = require('../../config/index')
      const maxDataSize = Constants.NETWORK_CONSTANTS.MAX_DATA_SIZE || 10 * 1024 * 1024 // 10MB限制
      if (jsonData.length > maxDataSize) {
        console.warn('数据过大，可能影响性能:', jsonData.length, '字符')
      }

      // 压缩数据
      let compressedData
      try {
        compressedData = DeflateDataCompressor.compress(jsonData)
      } catch (compressError) {
        console.error('数据压缩失败:', compressError.message)
        // 压缩失败时使用原始数据
        compressedData = jsonData
      }

      // 保存到存储
      try {
        wx.setStorageSync(this.storageKey, compressedData)
      } catch (storageError) {
        console.error('存储写入失败:', storageError.message)
        return false
      }

      // 计算压缩率
      const originalSize = jsonData.length
      const compressedSize = compressedData.length
      const compressionRatio = originalSize > 0 ?
        ((originalSize - compressedSize) / originalSize * 100).toFixed(2) : '0.00'

      console.log(`数据保存成功`)
      console.log(`原始大小: ${originalSize} 字符`)
      console.log(`压缩后大小: ${compressedSize} 字符`)
      console.log(`压缩率: ${compressionRatio}%`)

      return true

    } catch (error) {
      console.error('保存数据失败:', error.message)
      console.error('错误堆栈:', error.stack)
      return false
    }
  }

  /**
   * 准备保存的数据，转换Date对象为字符串
   * @param {Object} data - 要保存的数据
   * @returns {Object} 准备好的数据
   */
  prepareDataForSave(data) {
    // 深拷贝数据避免修改原始数据
    const dataToSave = JSON.parse(JSON.stringify(data, (key, value) => {
      // 将Date对象转换为ISO字符串
      if (value instanceof Date) {
        return value.toISOString()
      }
      return value
    }))
    
    return dataToSave
  }

  /**
   * 处理加载的数据，转换日期字符串为Date对象
   * @param {Object} data - 加载的数据
   * @returns {Object} 处理后的数据
   */
  processLoadedData(data) {
    // 深拷贝数据避免引用问题
    const processedData = JSON.parse(JSON.stringify(data))
    
    // 处理工作履历中的日期字段
    if (processedData.workHistory) {
      Object.values(processedData.workHistory).forEach(work => {
        // 转换工作履历的日期字段
        if (work.startDate) work.startDate = new Date(work.startDate)
        if (work.probationEndDate) work.probationEndDate = new Date(work.probationEndDate)
        if (work.endDate) work.endDate = new Date(work.endDate)
        if (work.createTime) work.createTime = new Date(work.createTime)
        if (work.updateTime) work.updateTime = new Date(work.updateTime)
        
        // 处理时间追踪数据中的日期字段
        if (work.timeTracking) {
          Object.values(work.timeTracking).forEach(dayData => {
            if (dayData.workDate) dayData.workDate = new Date(dayData.workDate)
            if (dayData.createTime) dayData.createTime = new Date(dayData.createTime)
            if (dayData.updateTime) dayData.updateTime = new Date(dayData.updateTime)
            
            // 处理时间段中的日期字段
            if (dayData.segments) {
              dayData.segments.forEach(segment => {
                // 注意：start 和 end 现在是分钟数格式，不需要转换为 Date 对象
                // 只转换真正的日期字段
                if (segment.createTime) segment.createTime = new Date(segment.createTime)
                if (segment.updateTime) segment.updateTime = new Date(segment.updateTime)
              })
            }
          })
        }
      })
    }
    
    // 转换根级别的日期字段
    if (processedData.createTime) processedData.createTime = new Date(processedData.createTime)
    if (processedData.lastModified) processedData.lastModified = new Date(processedData.lastModified)
    
    return processedData
  }

  /**
   * 清除所有存储数据
   * @returns {boolean} 清除是否成功
   */
  clearAllData() {
    try {
      wx.removeStorageSync(this.storageKey)
      console.log('所有存储数据已清除')
      return true
    } catch (error) {
      console.error('清除存储数据失败:', error)
      return false
    }
  }

  /**
   * 检查存储中是否有数据
   * @returns {boolean} 是否存在数据
   */
  hasStoredData() {
    try {
      const data = wx.getStorageSync(this.storageKey)
      return !!data
    } catch (error) {
      console.error('检查存储数据失败:', error)
      return false
    }
  }
}

// 创建并导出单例实例
module.exports = new StorageManager()
