/* 日历页面样式 - 集成版本 */

/* 容器样式 */
.calendar-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  padding-bottom: 200rpx;
}

/* 头部样式 */
.calendar-header {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  padding: 40rpx 32rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  margin: 24rpx 24rpx 0;
  border-radius: 20rpx;
}

.header-title {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.title-icon {
  font-size: 48rpx;
  margin-right: 16rpx;
}

.title-text {
  font-size: 40rpx;
  font-weight: bold;
  color: #2d3748;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.header-subtitle {
  font-size: 28rpx;
  color: #4a5568;
  line-height: 1.5;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 当前工作履历显示 */
.current-work-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16rpx;
  padding: 16rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
}

.work-info-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.work-icon {
  font-size: 24rpx;
  margin-right: 12rpx;
}

.work-name {
  font-size: 26rpx;
  font-weight: 500;
  color: #2d3748;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.work-switch-btn {
  padding: 8rpx 16rpx;
  background: white;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.work-switch-btn:active {
  background: rgba(0, 0, 0, 0.05);
  transform: scale(0.95);
}

/* 收入统计样式 */
.income-stats {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 32rpx;
  margin: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.stats-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 24rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.stats-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16rpx;
}

.stat-item {
  border-radius: 12rpx;
  padding: 24rpx 16rpx;
  text-align: center;
  border: 1rpx solid;
}

.stat-today {
  background-color: rgba(79, 70, 229, 0.05);
  border-color: rgba(79, 70, 229, 0.2);
}

.stat-week {
  background-color: rgba(16, 185, 129, 0.05);
  border-color: rgba(16, 185, 129, 0.2);
}

.stat-month {
  background-color: rgba(245, 158, 11, 0.05);
  border-color: rgba(245, 158, 11, 0.2);
}

.stat-label {
  font-size: 24rpx;
  color: #4a5568;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #2d3748;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 日历主体 */
.calendar-main {
  padding: 0 24rpx;
}

/* 日历主容器 */
.calendar-main {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  margin: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

/* 月份导航 */
.month-nav {
  padding: 24rpx 4rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.nav-btn:active {
  background: rgba(168, 237, 234, 0.3);
  transform: scale(0.95);
}

.year-nav-icon {
  font-size: 36rpx;
  color: #2d3748;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.month-nav-icon {
  font-size: 36rpx;
  color: #553c9a;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.month-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.year {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d3748;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.month {
  font-size: 36rpx;
  font-weight: 600;
  color: #553c9a;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 星期标题 */
.weekdays {
  padding: 20rpx 16rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
}

.weekday {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #4a5568;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 日历网格 */
.calendar-grid {
  padding: 16rpx 20rpx 20rpx;
  display: flex;
  flex-wrap: wrap;
}

.day-cell {
  width: calc(100% / 7);
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  box-sizing: border-box;
  padding: 4rpx;
}

.day-empty {
  pointer-events: none;
}

.day-active {
  cursor: pointer;
}

.day-active:active {
  transform: scale(0.95);
}

.day-number {
  font-size: 32rpx;
  font-weight: 500;
  color: #2d3748;
  z-index: 2;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 今天样式 */
.day-today {
  background: linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%);
  border: 2rpx solid #F87171;
}

.day-today .day-number {
  color: #DC2626;
  font-weight: bold;
}

.today-indicator {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  font-size: 16rpx;
  color: white;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6rpx;
  padding: 2rpx 6rpx;
}

/* 发薪日标识 */
.payday-indicator {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  font-size: 20rpx;
  z-index: 10;
}

/* 选中日期样式 */
.day-selected {
  background: linear-gradient(135deg, #E0E7FF 0%, #C7D2FE 100%);
  border: 2rpx solid #6366F1;
}

.day-selected .day-number {
  color: #4338CA;
  font-weight: bold;
}

/* 有数据的日期样式 - 移除，改用状态背景色 */
.day-has-data {
  /* 样式由状态类型决定 */
}

/* 数据指示点样式已移除 */

/* 状态指示器 */
.status-indicator {
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  z-index: 3;
}

.status-icon {
  font-size: 20rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
}

/* 节假日指示器 */
.holiday-indicator {
  position: absolute;
  bottom: 4rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
  max-width: 80rpx;
  overflow: hidden;
}

.holiday-text {
  font-size: 16rpx;
  color: #dc2626;
  font-weight: 600;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  text-align: center;
  display: block;
}

/* 日期状态样式 - 使用动态配置 */
/* 工作/出勤状态类 - 蓝色系 */
.day-status-work,
.day-status-rest,
.day-status-rotation_rest,
.day-status-compensatory_rest,
.day-status-work_suspension,
.day-status-standby,
.day-status-duty {
  background: var(--status-bg-color, #EFF6FF);
}

.day-status-work .day-number,
.day-status-rest .day-number,
.day-status-rotation_rest .day-number,
.day-status-compensatory_rest .day-number,
.day-status-work_suspension .day-number,
.day-status-standby .day-number,
.day-status-duty .day-number {
  color: var(--status-color, #3B82F6);
}

/* 法定/特殊假日类 - 绿色系 */
.day-status-holiday,
.day-status-weekend,
.day-status-adjusted_leave,
.day-status-annual_leave,
.day-status-festival_leave {
  background: var(--status-bg-color, #F0FDF4);
}

.day-status-holiday .day-number,
.day-status-weekend .day-number,
.day-status-adjusted_leave .day-number,
.day-status-annual_leave .day-number,
.day-status-festival_leave .day-number {
  color: var(--status-color, #10B981);
}

/* 请假/缺勤类 - 橙色系 */
.day-status-leave,
.day-status-sick,
.day-status-marriage_leave,
.day-status-maternity_leave,
.day-status-paternity_leave,
.day-status-bereavement_leave,
.day-status-work_injury_leave,
.day-status-family_visit_leave,
.day-status-absent {
  background: var(--status-bg-color, #FFFBEB);
}

.day-status-leave .day-number,
.day-status-sick .day-number,
.day-status-marriage_leave .day-number,
.day-status-maternity_leave .day-number,
.day-status-paternity_leave .day-number,
.day-status-bereavement_leave .day-number,
.day-status-work_injury_leave .day-number,
.day-status-family_visit_leave .day-number,
.day-status-absent .day-number {
  color: var(--status-color, #F59E0B);
}

/* 确保今天和选中状态的优先级 */
.day-today.day-selected {
  background: linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%);
  border: 2rpx solid #F87171;
}

.day-today.day-selected .day-number {
  color: #DC2626;
  font-weight: bold;
}

/* 不在任职日期范围内的日期样式 */
.day-outside-employment .day-number {
  color: #9ca3af !important; /* 灰色文字 */
  opacity: 0.6;
}

/* 确保不在任职范围内的日期在各种状态下都保持灰色 */
.day-outside-employment.day-today .day-number,
.day-outside-employment.day-selected .day-number,
.day-outside-employment.day-has-data .day-number {
  color: #9ca3af !important;
  opacity: 0.6;
}

/* 不在任职范围内的日期的状态样式也要调整 */
.day-outside-employment.day-status-work .day-number,
.day-outside-employment.day-status-leave .day-number,
.day-outside-employment.day-status-holiday .day-number,
.day-outside-employment.day-status-sick .day-number,
.day-outside-employment.day-status-rest .day-number {
  color: #9ca3af !important;
  opacity: 0.6;
}

/* 节假日样式中的不在任职范围内的日期 */
.day-outside-employment[data-holiday-type="holiday"] .day-number,
.day-outside-employment[data-holiday-type="workday"] .day-number,
.day-outside-employment[data-holiday-type="weekend"] .day-number {
  color: #9ca3af !important;
  opacity: 0.6;
}

/* 节假日样式 - 移除背景色和边框 */
.day-cell[data-holiday-type="holiday"] {
  /* 移除背景色和边框 */
}

.day-cell[data-holiday-type="holiday"] .day-number {
  color: #dc2626;
  font-weight: 600;
}

.day-cell[data-holiday-type="workday"] {
  /* 移除背景色和边框 */
}

.day-cell[data-holiday-type="workday"] .day-number {
  color: #3b82f6;
  font-weight: 600;
}

.day-cell[data-holiday-type="weekend"] {
  /* 移除周六日背景色和边框 */
}

.day-cell[data-holiday-type="weekend"] .day-number {
  color: #6b7280;
}

/* 选中日期信息 */
.selected-day-info {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 32rpx;
  margin: 0 24rpx 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.day-title {
  display: flex;
  align-items: center;
}

.date-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.date-text {
  font-size: 32rpx;
  color: #2d3748;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.day-income {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.income-label {
  font-size: 24rpx;
  color: #4a5568;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.income-value {
  font-size: 28rpx;
  color: #10B981;
  font-weight: bold;
}

/* 节假日信息显示 */
.holiday-info-display {
  margin: 16rpx 0;
}

.holiday-badge {
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
  gap: 8rpx;
  backdrop-filter: blur(8rpx);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(0, 0, 0, 0.1);
}

.holiday-name {
  font-size: 24rpx;
  font-weight: 600;
  color: #dc2626;
}

.holiday-work-status {
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  background: rgba(220, 38, 38, 0.1);
  color: #dc2626;
}

.holiday-badge.holiday-holiday .holiday-name {
  color: #dc2626;
}

.holiday-badge.holiday-holiday .holiday-work-status {
  background: rgba(220, 38, 38, 0.1);
  color: #dc2626;
}

.holiday-badge.holiday-workday .holiday-name {
  color: #3b82f6;
}

.holiday-badge.holiday-workday .holiday-work-status {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.holiday-badge.holiday-weekend .holiday-name {
  color: #6b7280;
}

.holiday-badge.holiday-weekend .holiday-work-status {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

/* 图表区域样式 */
.chart-section {
  margin-bottom: 24rpx;
}

/* 时间统计区域样式 */
.time-stats-section {
  margin-bottom: 24rpx;
}

.time-stats-grid {
  display: flex;
  gap: 16rpx;
  margin-top: 16rpx;
}

.time-stat-item {
  flex: 1;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-radius: 16rpx;
  padding: 24rpx 16rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.time-stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #e2e8f0, #cbd5e1);
}

.work-stat::before {
  background: linear-gradient(90deg, #dbeafe, #3b82f6);
}

.rest-stat::before {
  background: linear-gradient(90deg, #d1fae5, #10b981);
}

.overtime-stat::before {
  background: linear-gradient(90deg, #fef3c7, #f59e0b);
}

.stat-icon {
  font-size: 40rpx;
  margin-bottom: 12rpx;
  opacity: 0.8;
  display: block;
}

.stat-info {
  width: 100%;
}

.stat-label {
  font-size: 20rpx;
  color: #64748b;
  margin-bottom: 6rpx;
  display: block;
}

.stat-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #1e293b;
  display: block;
}

/* 时间段列表 */
.segments-section {
  margin-bottom: 24rpx;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.section-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.empty-segments {
  text-align: center;
  padding: 40rpx 20rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #4a5568;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.empty-tip {
  font-size: 24rpx;
  color: #718096;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.segments-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.segment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  position: relative;
  border-radius: 16rpx;
  margin-bottom: 12rpx;
  border: none;
}

.segment-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

/* 不同类型的背景色 */
.segment-work {
  background: linear-gradient(135deg, rgba(219, 234, 254, 0.8) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-left: 8rpx solid #3b82f6;
}

.segment-rest {
  background: linear-gradient(135deg, rgba(209, 250, 229, 0.8) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-left: 8rpx solid #10b981;
}

.segment-overtime {
  background: linear-gradient(135deg, rgba(254, 243, 199, 0.8) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-left: 8rpx solid #f59e0b;
}

.segment-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
}

.segment-right {
  text-align: right;
}

.segment-time {
  flex: 1;
}

.time-text {
  font-size: 28rpx;
  color: #2d3748;
  font-weight: 500;
  margin-bottom: 4rpx;
  display: block;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.duration-text {
  font-size: 20rpx;
  color: #4a5568;
  background: rgba(168, 237, 234, 0.2);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  display: inline-block;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.type-badge {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 600;
  color: white;
  min-width: 80rpx;
  text-align: center;
  white-space: nowrap;
}

.type-work {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.type-rest {
  background: linear-gradient(135deg, #10b981, #047857);
}

.type-overtime {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.segment-income {
  text-align: right;
}

.income-text {
  font-size: 24rpx;
  color: #10B981;
  font-weight: 600;
  display: block;
}

.rate-text {
  font-size: 20rpx;
  color: #4a5568;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 操作按钮 */
.day-actions {
  display: flex;
  gap: 16rpx;
}

.quick-actions {
  padding: 0 24rpx;
  display: flex;
  gap: 16rpx;
}

.quick-actions .action-btn {
  flex: 1;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(76, 81, 191, 0.4);
}

.action-btn.primary:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(76, 81, 191, 0.4);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  color: #2d3748;
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.action-btn.secondary:active {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(2rpx) scale(0.98);
}

.action-btn.danger {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1rpx solid rgba(239, 68, 68, 0.3);
  box-shadow: 0 4rpx 16rpx rgba(239, 68, 68, 0.15);
}

.action-btn.danger:active {
  background: rgba(239, 68, 68, 0.2);
  transform: translateY(2rpx) scale(0.98);
}

/* 摸鱼记录列表 */
.fishing-section {
  margin-top: 32rpx;
}

.fishing-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.fishing-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  border-left: 6rpx solid #fbbf24;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.fishing-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 10rpx,
    rgba(251, 191, 36, 0.1) 10rpx,
    rgba(251, 191, 36, 0.1) 20rpx
  );
  border-radius: 12rpx;
  pointer-events: none;
}

.fishing-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.fishing-badge {
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  background: #fbbf24;
  color: #fff;
}

.fishing-time {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.fishing-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.fishing-remark {
  max-width: 200rpx;
}

.remark-text {
  font-size: 24rpx;
  color: #6b7280;
  word-break: break-all;
  text-align: right;
}

.fishing-actions {
  display: flex;
  gap: 8rpx;
}

.action-btn-small {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.action-btn-small.edit {
  background: #e0f2fe;
  color: #0369a1;
}

.action-btn-small.edit:active {
  background: #bae6fd;
  transform: scale(0.95);
}

.action-btn-small.delete {
  background: #fef2f2;
  color: #dc2626;
}

.action-btn-small.delete:active {
  background: #fee2e2;
  transform: scale(0.95);
}

/* 添加摸鱼按钮 */
.add-fishing-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: #1890ff;
  border-radius: 20rpx;
  color: #fff;
  font-size: 22rpx;
  transition: all 0.3s ease;
}

.add-fishing-btn:active {
  background: #096dd9;
  transform: scale(0.95);
}

.add-icon {
  font-size: 24rpx;
  font-weight: 600;
}

.add-text {
  font-size: 22rpx;
  font-weight: 500;
}

/* 空状态 */
.empty-fishing {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 32rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #999;
}

/* 摸鱼价值信息 */
.fishing-value {
  display: flex;
  gap: 16rpx;
  margin: 8rpx 0;
  padding: 8rpx 12rpx;
  background: #f8f9fa;
  border-radius: 6rpx;
  border-left: 3rpx solid #fbbf24;
}

.value-item {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.value-label {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
}

.value-text {
  font-size: 22rpx;
  color: #333;
  font-weight: 600;
}

.btn-icon {
  margin-right: 12rpx;
  font-size: 28rpx;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(30rpx);
  border-radius: 24rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 24rpx 80rpx rgba(0, 0, 0, 0.4);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F1F5F9;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
}

.modal-close {
  font-size: 48rpx;
  color: #9CA3AF;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.modal-close:active {
  background-color: #F3F4F6;
}

.modal-body {
  padding: 32rpx;
  max-height: 40vh;
  overflow-y: scroll;
}

.modal-date {
  font-size: 32rpx;
  color: #2d3748;
  margin-bottom: 32rpx;
  text-align: center;
}

/* 统计信息区域 */
.schedule-summary {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  padding: 20rpx 24rpx;
  background-color: #F8FAFC;
  border-top: 1rpx solid #E2E8F0;
  border-bottom: 1rpx solid #E2E8F0;
  margin: 0;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
  flex: 1;
}

.summary-label {
  font-size: 20rpx;
  color: #64748B;
  font-weight: 500;
}

.summary-value {
  font-size: 20rpx;
  color: #1E293B;
  font-weight: 600;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.income-item .summary-label {
  color: #059669;
}

.income-value {
  color: #059669 !important;
  font-size: 20rpx;
  font-weight: 700;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #F1F5F9;
}

.modal-btn {
  flex: 1;
}

/* 输入组样式 */
.input-group-flex {
  display: flex;
  flex-direction: row;
  gap: 16rpx;
}

.input-group {
  margin-bottom: 32rpx;
}

.input-label {
  font-size: 28rpx;
  color: #2d3748;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.input-field {
  width: auto;
  padding: 24rpx;
  border: 1rpx solid #D1D5DB;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #FFFFFF;
}

.input-field:focus {
  border-color: #4F46E5;
  box-shadow: 0 0 0 4rpx rgba(79, 70, 229, 0.1);
}

.input-tip {
  font-size: 24rpx;
  color: #4a5568;
  margin-top: 8rpx;
}

/* 时间输入行样式 */
.time-inputs {
  overflow: hidden;
}

/* 时间段卡片布局 */
.time-input-card {
  background-color: #FFFFFF;
  border: 1rpx solid #E5E7EB;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  padding: 20rpx;
  transition: all 0.3s ease;
}

.time-input-card.conflict {
  border-color: #EF4444;
  background-color: #FEF2F2;
}

.time-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.time-duration {
  flex: 1;
  text-align: center;
}

.duration-text {
  font-size: 28rpx;
  color: #6B7280;
  font-weight: 500;
}

.time-setting-area {
  display: flex;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.time-input-section {
  flex: 1;
}

.time-label {
  display: block;
  font-size: 24rpx;
  color: #6B7280;
  margin-bottom: 8rpx;
}

.income-setting-area {
  padding-top: 16rpx;
  border-top: 1rpx solid #F3F4F6;
}

.income-input-section {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.income-label {
  font-size: 24rpx;
  color: #6B7280;
  min-width: 60rpx;
}

.income-info-section {
  display: flex;
  justify-content: flex-end;
}

.hourly-rate-text {
  font-size: 22rpx;
  color: #059669;
  background-color: #ECFDF5;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  border: 1rpx solid #A7F3D0;
}

.time-picker, .type-picker {
  background-color: #F9FAFB;
  border: 1rpx solid #E5E7EB;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 24rpx;
}

.picker-text {
  color: #2d3748;
  min-width: 100rpx;
  text-align: center;
}

/* 状态选择器样式 */

/* 新的状态选择器样式 */
.status-selector {
  width: 100%;
  height: 80rpx;
  background: #F9FAFB;
  border: 2rpx solid #E5E7EB;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.status-selector:active {
  background: #F3F4F6;
  border-color: #D1D5DB;
  transform: scale(0.98);
}

.status-selector .picker-text {
  display: flex;
  align-items: center;
  gap: 16rpx;
  width: 100%;
  height: 100%;
  justify-content: center;
  font-size: 28rpx;
  color: #2d3748;
  position: relative;
}

.status-selector .picker-text .selector-arrow {
  position: absolute;
  right: 24rpx;
  font-size: 32rpx;
  color: #718096;
  font-weight: bold;
}

.status-emoji {
  font-size: 32rpx;
}

.time-separator {
  color: #6B7280;
  font-size: 24rpx;
}

.remove-btn {
  color: #EF4444;
  font-size: 32rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.remove-btn:active {
  background-color: rgba(239, 68, 68, 0.1);
}

.add-time-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  color: #4F46E5;
  font-size: 28rpx;
  margin-top: 16rpx;
  border: 2rpx dashed #4F46E5;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.add-time-btn:active {
  background-color: rgba(79, 70, 229, 0.05);
}

  .add-icon {
    margin-right: 12rpx;
  }
  
  /* 批量操作模态框样式 */
  .modal-subtitle {
    font-size: 28rpx;
    font-weight: 600;
    color: #374151;
    margin-bottom: 24rpx;
  }
  
  .template-list {
    max-height: 400rpx;
    overflow-y: scroll;
    margin-bottom: 24rpx;
  }
  
  .template-item {
    background: #F9FAFB;
    border: 1rpx solid #E5E7EB;
    border-radius: 12rpx;
    padding: 24rpx;
    margin-bottom: 16rpx;
    transition: all 0.3s ease;
  }
  
  .template-item:active {
    background: #F3F4F6;
    transform: scale(0.98);
  }
  
  .template-selected {
    background: #EFF6FF;
    border-color: #3B82F6;
  }
  
  .template-date {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12rpx;
  }
  
  .date-text {
    font-size: 28rpx;
    font-weight: 600;
    color: #1F2937;
  }
  
  .status-badge {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 8rpx 12rpx;
    border-radius: 8rpx;
    font-size: 20rpx;
  }
  
  .status-icon {
    font-size: 24rpx;
  }
  
  .status-text {
    font-size: 20rpx;
    font-weight: 500;
  }
  
  .template-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 24rpx;
    color: #6B7280;
  }
  
  .segment-count {
    font-weight: 500;
  }
  
  .income-info {
    color: #10B981;
    font-weight: 600;
  }
  
  .empty-templates {
    text-align: center;
    padding: 80rpx 0;
    color: #9CA3AF;
  }
  
  .empty-icon {
    font-size: 80rpx;
    margin-bottom: 16rpx;
  }
  
  .empty-text {
    font-size: 28rpx;
    margin-bottom: 8rpx;
  }
  
  .empty-tip {
    font-size: 24rpx;
  }
  
  /* 复制选项样式 */
  .copy-options {
    margin-top: 32rpx;
    padding-top: 32rpx;
    border-top: 1rpx solid #E5E7EB;
  }
  
  .option-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 0;
    border-bottom: 1rpx solid #F3F4F6;
  }
  
  .option-item:last-child {
    border-bottom: none;
  }
  
  .option-label {
    font-size: 28rpx;
    color: #374151;
    font-weight: 500;
  }
  
  .option-switch {
    transform: scale(0.9);
  }

  /* 快速选择工作日样式 */
  .quick-select-section {
    margin: 24rpx 0;
    padding: 20rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16rpx;
    box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
  }

  .quick-select-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16rpx;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 12rpx;
  }

  .quick-select-btn:active {
    transform: scale(0.98);
    background: rgba(255, 255, 255, 0.1);
  }

  .quick-select-icon {
    font-size: 40rpx;
    margin-bottom: 8rpx;
    filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
  }

  .quick-select-text {
    font-size: 28rpx;
    font-weight: 600;
    color: white;
    margin-bottom: 4rpx;
    text-align: center;
  }

  .quick-select-tip {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
  }

  .clear-selection-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    margin-top: 16rpx;
    padding: 12rpx 24rpx;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20rpx;
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .clear-selection-btn:active {
    background: rgba(255, 255, 255, 0.3);
  }

  .clear-icon {
    font-size: 20rpx;
  }

  .clear-selection-btn text {
    font-size: 24rpx;
    color: white;
    font-weight: 500;
  }

  .holiday-status {
    margin-top: 8rpx;
  }

  .holiday-status .status-text {
    font-size: 20rpx;
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
  }
  
  /* 日期选择器样式 */
  .date-range-selector {
    margin-top: 32rpx;
    padding-top: 32rpx;
    border-top: 1rpx solid #E5E7EB;
  }
  
  .date-picker-row {
    display: flex;
    gap: 16rpx;
  }
  
  .date-picker-btn {
    flex: 1;
    background: #F9FAFB;
    border: 1rpx solid #E5E7EB;
    border-radius: 12rpx;
    padding: 24rpx;
    text-align: center;
    transition: all 0.3s ease;
  }
  
  .date-picker-btn:active {
    background: #F3F4F6;
    border-color: #D1D5DB;
  }
  
  .picker-label {
    font-size: 24rpx;
    color: #6B7280;
    display: block;
    margin-bottom: 8rpx;
  }
  
  .picker-value {
    font-size: 28rpx;
    color: #1F2937;
    font-weight: 500;
  }
  
  /* 批量复制日历样式 */
  .batch-modal-content {
    width: 90%;
    max-width: 800rpx;
    max-height: 85vh;
    overflow-y: auto;
  }
  
  .batch-step-info {
    margin-bottom: 30rpx;
    padding: 20rpx;
    background-color: #f8f9fa;
    border-radius: 12rpx;
    border-left: 4rpx solid #007aff;
  }
  
  .step-text {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #212529;
    margin-bottom: 8rpx;
  }
  
  .step-desc {
    font-size: 26rpx;
    color: #6c757d;
  }
  
  .batch-calendar {
    margin: 20rpx 0;
  }
  
  .batch-calendar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;
    padding: 0 20rpx;
  }
  
  .batch-calendar-nav {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #f8f9fa;
    border: 2rpx solid #e9ecef;
  }
  
  .batch-calendar-nav:active {
    background-color: #e9ecef;
  }
  
  .nav-arrow {
    font-size: 32rpx;
    color: #007aff;
    font-weight: bold;
  }
  
  .batch-calendar-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #212529;
  }
  
  .batch-calendar-weekdays {
    display: flex;
    margin-bottom: 10rpx;
  }
  
  .batch-calendar-weekdays .weekday {
    flex: 1;
    text-align: center;
    font-size: 26rpx;
    color: #6c757d;
    padding: 10rpx 0;
  }
  
  .batch-calendar-days {
    display: flex;
    flex-wrap: wrap;
  }
  
  .batch-calendar-day {
    width: 14.28%;
    aspect-ratio: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    margin-bottom: 10rpx;
    border-radius: 8rpx;
    transition: all 0.3s ease;
  }
  
  .batch-calendar-day.current-month {
    cursor: pointer;
  }
  
  .batch-calendar-day.has-data {
    background-color: #e3f2fd;
    box-shadow: inset 0 0 0 2rpx #2196f3;
  }
  
    .batch-calendar-day.selected-source {
    background-color: #4caf50 !important;
    box-shadow: inset 0 0 0 2rpx #388e3c !important;
  }

  .batch-calendar-day.selected-source .day-number {
    color: white !important;
    font-weight: 600;
  }

  .batch-calendar-day.selected-target {
    background-color: #ff9800 !important;
    box-shadow: inset 0 0 0 2rpx #f57c00 !important;
  }

  .batch-calendar-day.selected-target .day-number {
    color: white !important;
    font-weight: 600;
  }
  
  .batch-calendar-day.empty {
    pointer-events: none;
  }
  
  .batch-calendar-day .day-number {
    font-size: 26rpx;
    color: #212529;
    font-weight: 500;
    z-index: 1;
  }

  /* 批量日历中不在任职范围内的日期样式 */
  .batch-calendar-day.day-outside-employment .day-number {
    color: #9ca3af !important;
    opacity: 0.6;
  }

  /* 确保批量日历中不在任职范围内的日期在各种状态下都保持灰色 */
  .batch-calendar-day.day-outside-employment.has-data .day-number,
  .batch-calendar-day.day-outside-employment.selected-source .day-number,
  .batch-calendar-day.day-outside-employment.selected-target .day-number,
  .batch-calendar-day.day-outside-employment.selected .day-number {
    color: #9ca3af !important;
    opacity: 0.6;
  }
  
  .batch-calendar-day .day-indicator {
    position: absolute;
    top: 4rpx;
    right: 4rpx;
    width: 24rpx;
    height: 24rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .batch-calendar-day .indicator-text {
    font-size: 18rpx;
    color: white;
    font-weight: 600;
  }
  
  /* 工作安排预览 */
  .schedule-preview {
    margin: 20rpx 0;
    padding: 20rpx;
    background-color: #f8f9fa;
    border-radius: 12rpx;
    border: 2rpx solid #e9ecef;
  }
  
  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15rpx;
    padding-bottom: 15rpx;
    border-bottom: 1rpx solid #e9ecef;
  }
  
  .preview-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #212529;
  }
  
  .preview-income {
    font-size: 26rpx;
    color: #28a745;
    font-weight: 600;
  }
  
  .preview-segments {
    display: flex;
    flex-direction: column;
    gap: 12rpx;
  }
  
  .preview-segment {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12rpx 16rpx;
    background-color: white;
    border-radius: 8rpx;
    border: 1rpx solid #e9ecef;
  }
  
  .segment-time {
    font-size: 24rpx;
    color: #007aff;
    font-weight: 600;
  }
  
  .segment-type {
    font-size: 24rpx;
    color: #6c757d;
  }
  
  .segment-duration {
    font-size: 24rpx;
    color: #28a745;
    font-weight: 500;
  }
  
  /* 响应式设计 */
@media (max-width: 750rpx) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12rpx;
  }
  
  .month-title {
    flex-direction: column;
    gap: 4rpx;
  }
  
  .year, .month {
    font-size: 32rpx;
  }
  
  .day-cell {
    height: 100rpx;
  }
  
  .day-number {
    font-size: 28rpx;
  }
  
  .day-actions {
    flex-direction: column;
  }
  
  .time-input-card {
    margin-bottom: 12rpx;
    padding: 16rpx;
  }

  .time-card-header {
    margin-bottom: 12rpx;
  }

  .time-setting-area {
    flex-direction: column;
    gap: 12rpx;
    margin-bottom: 12rpx;
  }

  .time-input-section {
    flex: none;
  }

  .time-input-group {
    gap: 8rpx;
  }

  .time-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }

  .income-setting-area {
    padding-top: 12rpx;
  }

  .income-input-section {
    flex-wrap: wrap;
    gap: 8rpx;
    margin-bottom: 6rpx;
  }

  .income-info-section {
    justify-content: flex-start;
    margin-top: 6rpx;
  }

  .hourly-rate-text {
    font-size: 20rpx;
    padding: 3rpx 6rpx;
  }
} 

/* 引导界面样式 */
.no-work-guide {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.guide-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  max-width: 600rpx;
  width: 100%;
}

.guide-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.guide-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 24rpx;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.guide-text {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  font-size: 28rpx;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 48rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.guide-btn {
  background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  box-shadow: 0 8rpx 32rpx rgba(76, 81, 191, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.guide-btn:active {
  transform: scale(0.96);
  box-shadow: 0 6rpx 24rpx rgba(76, 81, 191, 0.5);
}

.btn-icon {
  font-size: 28rpx;
}

/* 主要内容区域 */
.calendar-content {
  /* 现有样式保持不变 */
}

/* 导入提示样式 */
.import-tip {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 24rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 16rpx;
  border: 2rpx solid rgba(102, 126, 234, 0.2);
}

.tip-icon {
  font-size: 32rpx;
  flex-shrink: 0;
}

.tip-text {
  font-size: 24rpx;
  color: #553c9a;
  line-height: 1.5;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 模态框按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
  color: white;
  padding: 28rpx 40rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(76, 81, 191, 0.4);
  transition: all 0.3s ease;
  border: none;
  text-align: center;
}

.btn-primary:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(76, 81, 191, 0.4);
}

.btn-primary.disabled {
  background: #E5E7EB;
  color: #9CA3AF;
  box-shadow: none;
  cursor: not-allowed;
}

.btn-primary.disabled:active {
  transform: none;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  color: #2d3748;
  padding: 28rpx 40rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  text-align: center;
}

.btn-secondary:active {
  background: rgba(255, 255, 255, 1);
  transform: translateY(2rpx) scale(0.98);
}

/* 导入日期详情样式 */
.import-date-detail {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-top: 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.detail-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
}

.detail-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-emoji {
  font-size: 24rpx;
}

.status-text {
  font-size: 24rpx;
  color: #4a5568;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.detail-income {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.income-label {
  font-size: 24rpx;
  color: #6B7280;
}

.income-value {
  font-size: 20rpx;
  font-weight: 600;
  color: #059669;
}

.detail-segments {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.segments-label {
  font-size: 24rpx;
  color: #6B7280;
}

.segment-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx 16rpx;
  background: rgba(249, 250, 251, 0.8);
  border-radius: 8rpx;
  border: 1rpx solid rgba(229, 231, 235, 0.5);
}

.segment-type {
  font-size: 22rpx;
  color: #667eea;
  font-weight: 500;
  min-width: 60rpx;
}

.segment-time {
  font-size: 24rpx;
  color: #2d3748;
  font-weight: 500;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 日历选中状态样式 */
.batch-calendar-day.selected {
  background: rgba(102, 126, 234, 0.2);
  border: 2rpx solid #667eea;
  transform: scale(1.1);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

/* 无时间安排占位提示样式 */
.no-segments-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 24rpx;
  background: rgba(249, 250, 251, 0.6);
  border-radius: 12rpx;
  border: 2rpx dashed #E5E7EB;
  gap: 12rpx;
}

.placeholder-icon {
  font-size: 32rpx;
  opacity: 0.5;
}

.placeholder-text {
  font-size: 24rpx;
  color: #9CA3AF;
  font-weight: 500;
}

/* 智能填写收入按钮 */
.input-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.time-summary {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.total-income-text {
  font-size: 26rpx;
  color: #059669;
  font-weight: 600;
  background-color: #ECFDF5;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  border: 1rpx solid #A7F3D0;
}

.smart-income-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  background: linear-gradient(135deg, #3B82F6, #1D4ED8);
  color: white;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.smart-income-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.4);
}

/* 冲突提示样式 */
.conflict-warning {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background-color: #FEF2F2;
  border: 1rpx solid #FECACA;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
}

.warning-icon {
  font-size: 28rpx;
}

.warning-text {
  font-size: 24rpx;
  color: #DC2626;
  font-weight: 500;
}

.conflict-indicator {
  margin-top: 12rpx;
  padding: 8rpx 12rpx;
  background-color: #FEE2E2;
  border-radius: 6rpx;
  border-left: 4rpx solid #EF4444;
}

.conflict-text {
  font-size: 22rpx;
  color: #DC2626;
  font-weight: 500;
}

/* 时间输入组 */
.time-input-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.next-day-checkbox {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 20rpx;
  color: #6B7280;
}

.checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #D1D5DB;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
  color: white;
  background: white;
  transition: all 0.2s ease;
}

.checkbox.checked {
  background: #3B82F6;
  border-color: #3B82F6;
}

.checkbox-label {
  font-size: 20rpx;
  color: #6B7280;
}

/* 收入输入组 */
.income-input-group {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: #F9FAFB;
  border: 2rpx solid #E5E7EB;
  border-radius: 12rpx;
  padding: 8rpx 12rpx;
  min-width: 120rpx;
}

.income-input {
  flex: 1;
  font-size: 24rpx;
  color: #1F2937;
  background: transparent;
  border: none;
  outline: none;
  text-align: center;
}

.income-unit {
  font-size: 20rpx;
  color: #6B7280;
  font-weight: 500;
}

/* 智能填写收入模态框 */
.smart-income-modal {
  max-width: 600rpx;
  width: 90vw;
}

/* 输入框与计算器按钮的横向布局 */
.input-with-calculator {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input-with-calculator .input-with-unit {
  flex: 1;
}

/* 日收入计算器按钮 */
.daily-income-calculator-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  padding: 8rpx 16rpx;
  margin-bottom: 16rpx;
  background: linear-gradient(135deg, #10B981, #059669);
  border-radius: 12rpx;
  color: white;
  font-size: 22rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.3);
  transition: all 0.3s ease;
  white-space: nowrap;
  flex-shrink: 0;
}

.daily-income-calculator-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.4);
}

.calculator-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.calculator-text {
  font-size: 22rpx;
  font-weight: 500;
}

/* 日收入计算器模态框 */
.daily-income-calculator-modal {
  max-width: 600rpx;
  width: 90vw;
}

.calculator-description {
  margin-bottom: 24rpx;
  padding: 16rpx;
  background: #F0F9FF;
  border-radius: 12rpx;
  border-left: 4rpx solid #0EA5E9;
}

.description-text {
  font-size: 26rpx;
  color: #0369A1;
  line-height: 1.4;
}

.calculation-result {
  margin-top: 32rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #F0FDF4, #ECFDF5);
  border-radius: 16rpx;
  border: 2rpx solid #10B981;
  text-align: center;
}

.result-label {
  font-size: 26rpx;
  color: #065F46;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.result-value {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 8rpx;
}

.result-amount {
  font-size: 48rpx;
  color: #059669;
  font-weight: 700;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.result-unit {
  font-size: 28rpx;
  color: #065F46;
  font-weight: 500;
}

.calculation-modes {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.mode-option {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background: #F9FAFB;
  border: 2rpx solid #E5E7EB;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.mode-option.active {
  background: linear-gradient(135deg, #EBF4FF, #DBEAFE);
  border-color: #3B82F6;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.15);
}

.mode-icon {
  font-size: 40rpx;
  width: 60rpx;
  text-align: center;
}

.mode-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.mode-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1F2937;
}

.mode-desc {
  font-size: 24rpx;
  color: #6B7280;
  line-height: 1.4;
}

/* 计算方式选择器 */
.calculation-method-selector {
  display: flex;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.method-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 12rpx;
  background: #F9FAFB;
  border: 2rpx solid #E5E7EB;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  text-align: center;
}

.method-option.active {
  background: linear-gradient(135deg, #EBF4FF, #DBEAFE);
  border-color: #3B82F6;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.15);
}

.method-option:hover {
  background: #F3F4F6;
  border-color: #D1D5DB;
}

.method-option.active:hover {
  background: linear-gradient(135deg, #DBEAFE, #BFDBFE);
}

.method-icon {
  font-size: 32rpx;
  margin-bottom: 4rpx;
}

.method-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #1F2937;
  margin-bottom: 2rpx;
}

.method-desc {
  font-size: 22rpx;
  color: #6B7280;
  font-weight: 500;
  line-height: 1.3;
}

.calculation-content {
  margin-top: 24rpx;
}

.input-with-unit {
  display: flex;
  align-items: center;
  background: #F9FAFB;
  border: 2rpx solid #E5E7EB;
  border-radius: 12rpx;
  padding: 16rpx;
  gap: 12rpx;
}

.number-input {
  flex: 1;
  font-size: 28rpx;
  color: #1F2937;
  background: transparent;
  border: none;
  outline: none;
}

.input-unit {
  font-size: 24rpx;
  color: #6B7280;
  font-weight: 500;
  white-space: nowrap;
}

.time-segment-card {
  background-color: #FFFFFF;
  border: 1rpx solid #E5E7EB;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.time-segment-card.conflict {
  border-color: #EF4444;
  background-color: #FEF2F2;
  box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.1);
}

/* 时间段头部 */
.segment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.segment-type-picker {
  width: 150rpx;
  flex-shrink: 0;
}

.segment-type-display {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background-color: #F8FAFC;
  border: 1rpx solid #E2E8F0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1E293B;
}

.type-icon {
  font-size: 24rpx;
  margin-right: 10rpx;
}

.type-text {
  font-weight: 500;
}

.segment-duration {
  margin: 0 20rpx;
  text-align: center;
  flex: 1;
}

.duration-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #71a5f9;
  background-color: #EFF6FF;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.segment-remove-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F3F4F6;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.segment-remove-btn:active {
  background-color: #5EE7EB;
  transform: scale(0.95);
}

.remove-icon {
  font-size: 32rpx;
  color: #EF4444;
  font-weight: bold;
}

/* 时间设置行 */
.time-setting-row {
  margin-bottom: 24rpx;
}

/* 时间范围选择器 */
.time-range-selector {
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease;
  cursor: pointer;
}

.time-range-selector:active {
  background: #e2e8f0;
  transform: scale(0.98);
}

.time-range-display {
  flex: 1;
}

.time-range-text {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.start-time, .end-time {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
}

.time-separator {
  font-size: 28rpx;
  color: #64748b;
  font-weight: 500;
}

.start-next-day-indicator,
.end-next-day-indicator {
  background: #fef3c7;
  color: #d97706;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  font-weight: 600;
  margin-left: 8rpx;
}

.time-range-duration {
  margin-top: 4rpx;
}

.duration-text {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
}

.time-range-arrow {
  margin-left: 16rpx;
}

.arrow-icon {
  font-size: 32rpx;
  color: #94a3b8;
  font-weight: bold;
}

.time-field {
  flex: 1;
}

.time-field-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.time-field-label {
  font-size: 24rpx;
  color: #6B7280;
}

.time-picker-input {
  width: 100%;
}

.time-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background-color: #FFFFFF;
  border: 2rpx solid #E2E8F0;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #1b6495;
  transition: all 0.2s ease;
}

.time-display:active {
  border-color: #3B82F6;
  background-color: #F8FAFC;
}

.time-value {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.time-icon {
  font-size: 28rpx;
  opacity: 0.6;
}

.next-day-toggle {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 10rpx;
  border-radius: 6rpx;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 22rpx;
}

.next-day-toggle.active {
  background-color: #3B82F6;
  border-color: #3B82F6;
}

.next-day-toggle.active .toggle-checkbox,
.next-day-toggle.active .toggle-label {
  color: #FFFFFF;
}

.toggle-checkbox {
  width: 24rpx;
  height: 24rpx;
  border: 2rpx solid #CBD5E1;
  border-radius: 3rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
  font-weight: bold;
  background-color: #FFFFFF;
  transition: all 0.2s ease;
}

.next-day-toggle.active .toggle-checkbox {
  border-color: #FFFFFF;
  background-color: #FFFFFF;
  color: #3B82F6;
}

.toggle-label {
  font-size: 22rpx;
  color: #64748B;
  font-weight: 500;
}

/* 收入设置行 */
.income-setting-row {
  display: flex;
  gap: 24rpx;
}

.income-field {
  flex: 1;
}

.income-field-label {
  display: block;
  font-size: 24rpx;
  color: #6B7280;
  margin-bottom: 12rpx;
}

.income-input-wrapper {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.income-input-field {
  flex: 1;
  padding: 16rpx 20rpx;
  background-color: #FFFFFF;
  border: 2rpx solid #E2E8F0;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #00a6ff;
  text-align: right;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.income-input-field:focus {
  border-color: #7C3AED;
  background-color: #FEFBFF;
}

.income-unit {
  font-size: 24rpx;
  color: #6B7280;
  font-weight: 500;
}

.hourly-rate-field {
  flex: 1;
}

.hourly-rate-label {
  display: block;
  font-size: 24rpx;
  color: #6B7280;
  margin-bottom: 12rpx;
}

.hourly-rate-input-wrapper {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.hourly-rate-input-field {
  flex: 1;
  padding: 16rpx 20rpx;
  background-color: #FFFFFF;
  border: 2rpx solid #E2E8F0;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #059669;
  text-align: right;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.hourly-rate-input-field:focus {
  border-color: #059669;
  background-color: #F0FDF4;
}

.hourly-rate-unit {
  font-size: 24rpx;
  color: #059669;
  font-weight: 500;
}

/* 时间段警告提示 */
.segment-warning {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-top: 16rpx;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  animation: warning-pulse 2s ease-in-out infinite;
}

/* 不同类型的警告样式 */
.time-segment-card.conflict .segment-warning {
  background-color: #FEF2F2;
  border: 1rpx solid #FECACA;
}

.warning-icon {
  font-size: 28rpx;
  flex-shrink: 0;
}

.warning-message {
  font-size: 24rpx;
  color: #DC2626;
  font-weight: 500;
  flex: 1;
}

/* 警告动画效果 */
@keyframes warning-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 响应式设计 - 移动端优化 */
@media (max-width: 750rpx) {
  .time-segment-card {
    margin-bottom: 16rpx;
    padding: 20rpx;
  }

  .segment-header {
    flex-wrap: wrap;
    gap: 16rpx;
    margin-bottom: 20rpx;
  }

  .segment-type-picker {
    width: 160rpx;
  }

  .segment-duration {
    margin: 0;
    text-align: left;
    flex: 1;
    min-width: 120rpx;
  }

  .segment-remove-btn {
    width: 40rpx;
    height: 40rpx;
  }

  .time-field-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }

  .next-day-toggle {
    align-self: flex-end;
  }

  .time-setting-row {
    flex-direction: column;
    gap: 16rpx;
    margin-bottom: 20rpx;
  }

  .income-setting-row {
    flex-direction: column;
    gap: 16rpx;
  }

  .time-display {
    font-size: 28rpx;
  }

  .income-input-field {
    font-size: 28rpx;
  }

  .hourly-rate-input-field {
    font-size: 28rpx;
  }

  .segment-type-display {
    font-size: 26rpx;
  }

  .duration-value {
    font-size: 26rpx;
  }

  /* 统计信息响应式 */
  .schedule-summary {
    padding: 16rpx 20rpx;
  }

  .summary-label {
    font-size: 20rpx;
  }

  .summary-value {
    font-size: 20rpx;
  }

  .income-value {
    font-size: 20rpx;
  }
}

/* 收入调整部分样式 */
.income-adjustment-section {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  margin: 24rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.adjustment-actions {
  display: flex;
  gap: 16rpx;
}

.add-adjustment-btn {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  transition: all 0.2s ease;
  cursor: pointer;
}

.add-adjustment-btn.income {
  background-color: #e8f5e8;
  color: #2e7d32;
  border: 1rpx solid #c8e6c9;
}

.add-adjustment-btn.income:active {
  background-color: #c8e6c9;
}

.add-adjustment-btn.deduction {
  background-color: #ffebee;
  color: #c62828;
  border: 1rpx solid #ffcdd2;
}

.add-adjustment-btn.deduction:active {
  background-color: #ffcdd2;
}

.add-adjustment-btn .add-icon {
  font-size: 20rpx;
  margin-right: 8rpx;
}

.add-adjustment-btn .add-text {
  font-weight: 500;
}

/* 收入调整汇总 */
.adjustment-summary {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  margin: 24rpx 0;
  border: 1rpx solid #e9ecef;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.summary-item:last-child {
  margin-bottom: 0;
  padding-top: 12rpx;
  border-top: 1rpx solid #dee2e6;
  font-weight: 600;
}

.summary-label {
  font-size: 28rpx;
  color: #495057;
}

.summary-value {
  font-size: 28rpx;
  font-weight: 600;
}

.summary-value.positive {
  color: #28a745;
}

.summary-value.negative {
  color: #dc3545;
}

/* 收入调整列表 */
.adjustment-list {
  margin: 24rpx 0;
}

.adjustment-category-title {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.category-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.adjustment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  background-color: #ffffff;
}

.income-item {
  border-left: 6rpx solid #28a745;
}

.deduction-item {
  border-left: 6rpx solid #dc3545;
}

.adjustment-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 80rpx; /* 确保有足够的高度用于居中 */
}

.adjustment-type {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

/* 只有当有描述时才添加下边距 */
.adjustment-left .adjustment-type:not(:last-child) {
  margin-bottom: 4rpx;
}

.adjustment-description {
  font-size: 24rpx;
  color: #666666;
}

.adjustment-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.adjustment-amount {
  font-size: 28rpx;
  font-weight: 600;
}

.adjustment-actions {
  display: flex;
  gap: 8rpx;
}

/* 空状态 */
.empty-adjustments {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 32rpx;
  text-align: center;
}

.empty-adjustments .empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-adjustments .empty-text {
  font-size: 32rpx;
  color: #666666;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.empty-adjustments .empty-tip {
  font-size: 26rpx;
  color: #999999;
  line-height: 1.5;
}