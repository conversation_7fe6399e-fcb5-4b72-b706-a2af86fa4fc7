/**
 * 时间段服务类 - 重构版
 * 基于新的全局数据管理器实现时间段数据管理
 * 
 * 重构说明：
 * - 移除了复杂的缓存机制
 * - 简化了API设计
 * - 使用统一的数据管理器
 * - 时间追踪数据已整合到工作履历中
 */

const { formatDate, formatDateKey, getDateStart, getDateEnd, isSameDay, calculateIncome } = require('../../utils/helpers/time-utils.js')
const { minutesToTimeDisplay, formatDuration } = require('../../utils/helpers/time-utils.js')
const dataManager = require('../managers/data-manager.js')

class TimeSegmentService {
  constructor() {
    // 获取全局数据管理器实例
    this.dataManager = dataManager
  }

  /**
   * 获取指定工作履历的所有日期数据
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @returns {Object} 按日期分组的数据对象
   */
  getAllDailyData(workId) {
    try {
      // 如果没有指定工作履历ID，使用当前工作
      if (!workId) {
        const currentWork = this.dataManager.getCurrentWork()
        if (!currentWork) {
          console.warn('没有可用的工作履历ID')
          return {}
        }
        workId = currentWork.id
      }

      // 获取指定工作履历的每日数据
      return this.dataManager.getTimeTracking(workId)
    } catch (error) {
      console.error('获取日期数据失败:', error)
      return {}
    }
  }

  /**
   * 获取指定日期的数据
   * @param {Date} date - 日期
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @returns {Object} 该日期的数据
   */
  getDayData(date, workId) {
    try {
      // 如果没有指定工作履历ID，使用当前工作
      if (!workId) {
        const currentWork = this.dataManager.getCurrentWork()
        if (!currentWork) {
          console.warn('没有可用的工作履历ID')
          return this.getDefaultDayData(date)
        }
        workId = currentWork.id
      }

      const dayData = this.dataManager.getDayData(workId, date)

      // 创建新对象，不修改原始数据
      const enrichedDayData = {
        ...dayData
      }

      // 转换时间段数据为显示格式
      if (enrichedDayData && enrichedDayData.segments) {
        enrichedDayData.segments = enrichedDayData.segments.map(segment => this.convertSegmentForDisplay(segment))

        // 动态计算统计信息
        const stats = this.calculateStatsFromSegments(dayData.segments)
        enrichedDayData.totalWorkMinutes = stats.totalWorkMinutes
        enrichedDayData.hourlyRate = stats.averageHourlyRate

        // 如果没有存储的日收入，动态计算
        if (!enrichedDayData.dailyIncome && enrichedDayData.dailyIncome !== 0) {
          enrichedDayData.dailyIncome = this.calculateDailyIncome(dayData.segments)
        }
      }

      return enrichedDayData
    } catch (error) {
      console.error('获取日期数据失败:', error)
      return this.getDefaultDayData(date)
    }
  }

  /**
   * 获取默认的日期数据
   * @param {Date} date - 日期
   * @returns {Object} 默认日期数据
   */
  getDefaultDayData(date) {
    return {
      workDate: getDateStart(date),
      dailyIncome: 0,
      segments: [],
      fishes: [], // 摸鱼数据数组
      // 新增：额外收入和扣款相关字段
      extraIncome: 0,
      deductions: 0,
      netIncome: 0,
      extraIncomeItems: [],
      deductionItems: [],
      status: 'work', // 默认状态为工作日
      createTime: new Date(),
      updateTime: new Date()
    }
  }

  /**
   * 将时间段数据转换为显示格式（不修改原始数据）
   * @param {Object} segment - 原始时间段数据
   * @returns {Object} 转换后的时间段数据（新对象）
   */
  convertSegmentForDisplay(segment) {
    if (!segment) return segment

    const duration = segment.end - segment.start

    // 动态计算时薪（如果不是休息时间且有收入）
    const hourlyRate = (segment.type !== 'rest' && segment.income > 0)
      ? segment.income / (duration / 60)
      : 0

    // 创建新对象，不修改原始数据
    return {
      // 核心数据
      id: segment.id,
      start: segment.start,
      end: segment.end,
      type: segment.type,
      income: segment.income || 0,

      // 动态计算的数据（不保存到存储）
      hourlyRate: hourlyRate,
      startTime: minutesToTimeDisplay(segment.start),
      endTime: minutesToTimeDisplay(segment.end),
      duration: formatDuration(duration),
      durationMinutes: duration,
      typeText: this.getTypeText(segment.type)
    }
  }

  /**
   * 转换日期数据为显示格式
   * @param {Object} dayData - 日期数据
   * @returns {Object} 显示格式的日期数据
   */
  convertDayDataForDisplay(dayData) {
    if (!dayData) return dayData

    const segments = dayData.segments || []

    return {
      date: dayData.date,
      dailyIncome: this.calculateDailyIncome(segments),
      totalWorkMinutes: this.calculateWorkMinutes(segments),
      totalWorkHours: this.calculateWorkMinutes(segments) / 60,
      averageHourlyRate: this.calculateAverageHourlyRate(segments),
      segmentCount: segments.length,
      workSegmentCount: segments.filter(s => s.type !== 'rest').length,
      segments: segments.map(segment => this.convertSegmentForDisplay(segment)),
      fishes: dayData.fishes || [], // 保留摸鱼数据
      status: dayData.status || 'active'
    }
  }

  /**
   * 获取类型文本
   * @param {string} type - 时间段类型
   * @returns {string} 类型文本
   */
  getTypeText(type) {
    const typeMap = {
      work: '工作',
      rest: '休息',
      overtime: '加班'
    }
    return typeMap[type] || '未知类型'
  }



  /**
   * 保存指定日期的数据
   * @param {Date} date - 日期
   * @param {Object} dayData - 该日期的数据
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   */
  saveDayData(date, dayData, workId) {
    try {
      // 如果没有指定工作履历ID，使用当前工作
      if (!workId) {
        const currentWork = this.dataManager.getCurrentWork()
        if (!currentWork) {
          throw new Error('没有可用的工作履历ID')
        }
        workId = currentWork.id
      }

      // 清理数据，只保存核心字段，移除可计算的冗余字段
      const cleanedDayData = this.cleanDayDataForStorage(dayData)

      this.dataManager.saveDayData(workId, date, cleanedDayData)
    } catch (error) {
      console.error('保存日期数据失败:', error)
      throw new Error('数据保存失败')
    }
  }

  /**
   * 设置指定日期的工作计划
   * @param {Date} date - 工作日期
   * @param {Array} timeInputs - 时间段输入数组
   * @param {number} dailyIncome - 当日总收入（可选）
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   */
  setDaySchedule(date, timeInputs, dailyIncome = 0, workId) {
    try {
      const workDate = getDateStart(date)

      // 计算工作时间总时长（排除休息时间）
      let totalWorkMinutes = 0
      let totalIncome = 0
      const segments = []

      timeInputs.forEach((input, index) => {
        // 解析时间为分钟数
        const [startHours, startMinutes] = input.startTime.split(':').map(Number)
        const [endHours, endMinutes] = input.endTime.split(':').map(Number)

        let startMinutesTotal = startHours * 60 + startMinutes
        let endMinutesTotal = endHours * 60 + endMinutes

        // 处理跨日情况
        const isStartNextDay = input.isStartNextDay || false
        const isEndNextDay = input.isEndNextDay || false

        if (isStartNextDay) {
          startMinutesTotal += 24 * 60
        }

        if (isEndNextDay) {
          endMinutesTotal += 24 * 60
        }

        // 如果没有明确的跨日标记，使用传统逻辑判断
        if (!isStartNextDay && !isEndNextDay && endMinutesTotal <= startMinutesTotal) {
          endMinutesTotal += 24 * 60
        }

        const segment = {
          id: index,
          start: startMinutesTotal,
          end: endMinutesTotal,
          type: input.type,
          income: input.income || 0
        }

        segments.push(segment)

        // 计算工作时间和收入（排除休息）
        if (input.type !== 'rest') {
          const minutes = endMinutesTotal - startMinutesTotal
          totalWorkMinutes += minutes
          totalIncome += segment.income
        }
      })

      // 按时间排序
      segments.sort((a, b) => a.start - b.start)

      // 如果没有提供总收入，使用各时间段收入之和
      const finalDailyIncome = dailyIncome > 0 ? dailyIncome : totalIncome

      // 获取现有的摸鱼数据
      const existingDayData = this.getDayData(date, workId)
      const existingFishes = existingDayData.fishes || []

      // 检查摸鱼数据是否与新的时间段冲突
      const fishingConflicts = this.checkFishingConflictsWithSegments(existingFishes, segments)

      if (fishingConflicts.hasConflicts) {
        return {
          success: false,
          message: '时间段修改会导致摸鱼数据冲突，请先处理相关摸鱼记录',
          conflicts: fishingConflicts.conflicts,
          requiresFishingUpdate: true,
          existingFishes: existingFishes // 返回现有摸鱼数据供处理
        }
      }

      // 保留现有的日期状态，如果没有则默认为 'work'
      const existingStatus = existingDayData.status || 'work'

      const dayData = {
        date: workDate.toISOString().split('T')[0], // YYYY-MM-DD 格式
        segments,
        fishes: existingFishes, // 保留现有的摸鱼数据
        dailyIncome: finalDailyIncome,
        status: existingStatus, // 保留现有状态
        updateTime: new Date()
      }
      
      this.saveDayData(date, dayData, workId)
      
      return dayData
    } catch (error) {
      console.error('设置工作计划失败:', error)
      throw new Error('设置工作计划失败')
    }
  }

  /**
   * 删除指定日期的数据
   * @param {Date} date - 日期
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   */
  clearDayData(date, workId) {
    try {
      // 如果没有指定工作履历ID，使用当前工作
      if (!workId) {
        const currentWork = this.dataManager.getCurrentWork()
        if (!currentWork) {
          throw new Error('没有可用的工作履历ID')
        }
        workId = currentWork.id
      }
      
      const dateKey = this.dataManager.formatDateKey(date)
      const timeTracking = this.dataManager.getTimeTracking(workId)
      
      if (timeTracking[dateKey]) {
        delete timeTracking[dateKey]
        // 更新工作履历中的时间追踪数据
        this.dataManager.updateWork(workId, { timeTracking })
      }
    } catch (error) {
      console.error('清除日期数据失败:', error)
      throw new Error('清除日期数据失败')
    }
  }

  /**
   * 检查日期是否在范围内
   * @param {Date} date - 要检查的日期
   * @param {Array} dateRange - 日期范围 [startDate, endDate]
   * @returns {boolean} 是否在范围内
   */
  isDateInRange(date, dateRange) {
    if (!dateRange || dateRange.length !== 2) {
      return true
    }
    
    const targetDate = getDateStart(date)
    const startDate = getDateStart(dateRange[0])
    const endDate = getDateStart(dateRange[1])
    
    return targetDate >= startDate && targetDate <= endDate
  }

  /**
   * 计算时间统计
   * @param {Array} dateRange - 日期范围（可选）
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @returns {Object} 时间统计对象
   */
  calculateTimeStatistics(dateRange = null, workId) {
    try {
      const allData = this.getAllDailyData(workId)
      
      const stats = {
        work: 0,      // 工作时间（分钟）
        rest: 0,      // 休息时间（分钟）
        overtime: 0   // 加班时间（分钟）
      }
      
      Object.values(allData).forEach(dayData => {
        if (dateRange && !this.isDateInRange(dayData.workDate, dateRange)) {
          return
        }
        
        if (dayData.segments) {
          dayData.segments.forEach(segment => {
            const duration = segment.end - segment.start

            if (stats.hasOwnProperty(segment.type)) {
              stats[segment.type] += duration
            }
          })
        }
      })
      
      return stats
    } catch (error) {
      console.error('计算时间统计失败:', error)
      return { work: 0, rest: 0, overtime: 0 }
    }
  }

  /**
   * 计算收入统计
   * @param {Array} dateRange - 日期范围（可选）
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @returns {Object} 收入统计对象
   */
  calculateIncomeStatistics(dateRange = null, workId) {
    try {
      const allData = this.getAllDailyData(workId)
      
      let totalIncome = 0
      let workDays = 0
      
      Object.values(allData).forEach(dayData => {
        if (dateRange && !this.isDateInRange(dayData.workDate, dateRange)) {
          return
        }
        
        if (dayData.dailyIncome > 0) {
          totalIncome += dayData.dailyIncome
          workDays++
        }
      })
      
      return {
        totalIncome,
        workDays,
        averageIncome: workDays > 0 ? totalIncome / workDays : 0
      }
    } catch (error) {
      console.error('计算收入统计失败:', error)
      return { totalIncome: 0, workDays: 0, averageIncome: 0 }
    }
  }

  /**
   * 获取有数据的日期列表
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @returns {Array} 日期数组
   */
  getDatesWithData(workId) {
    try {
      const allData = this.getAllDailyData(workId)
      
      return Object.keys(allData)
        .map(dateKey => new Date(dateKey))
        .filter(date => {
          const dayData = allData[this.dataManager.formatDateKey(date)]
          return dayData.segments && dayData.segments.length > 0
        })
        .sort((a, b) => b - a) // 按日期倒序排列
    } catch (error) {
      console.error('获取有数据的日期失败:', error)
      return []
    }
  }

  /**
   * 复制日期数据
   * @param {Date} sourceDate - 源日期
   * @param {Date} targetDate - 目标日期
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @param {Object} options - 复制选项
   */
  copyDayData(sourceDate, targetDate, workId, options = {}) {
    try {
      const sourceDayData = this.getDayData(sourceDate, workId)
      
      if (!sourceDayData.segments || sourceDayData.segments.length === 0) {
        throw new Error('源日期没有数据')
      }
      
      // 复制数据
      const newDayData = {
        date: targetDate.toISOString().split('T')[0],
        status: options.copyStatus !== false ? sourceDayData.status : 'active',
        segments: sourceDayData.segments.map((segment, index) => ({
          id: index,
          start: segment.start,
          end: segment.end,
          type: segment.type,
          income: options.copyIncome !== false ? segment.income : 0
        }))
      }
      
      this.saveDayData(targetDate, newDayData, workId)
    } catch (error) {
      console.error('复制日期数据失败:', error)
      throw new Error('复制日期数据失败')
    }
  }

  /**
   * 批量复制工作安排到多个日期
   * @param {Date} sourceDate - 源日期
   * @param {Array} targetDates - 目标日期数组
   * @param {boolean} copyStatus - 是否复制状态
   * @param {boolean} copyIncome - 是否复制收入
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   */
  copyScheduleToOtherDates(sourceDate, targetDates, copyStatus = true, copyIncome = true, workId) {
    try {
      if (!sourceDate || !targetDates || targetDates.length === 0) {
        throw new Error('源日期或目标日期无效')
      }

      const options = {
        copyStatus,
        copyIncome
      }

      let successCount = 0
      let failCount = 0

      for (const targetDate of targetDates) {
        try {
          this.copyDayData(sourceDate, targetDate, workId, options)
          successCount++
        } catch (error) {
          console.error(`复制到${formatDate(targetDate)}失败:`, error)
          failCount++
        }
      }

      console.log(`批量复制完成: 成功${successCount}个, 失败${failCount}个`)

      if (failCount > 0 && successCount === 0) {
        throw new Error(`所有复制操作均失败`)
      }

      return {
        success: successCount,
        failed: failCount
      }
    } catch (error) {
      console.error('批量复制工作安排失败:', error)
      throw new Error('批量复制失败: ' + error.message)
    }
  }



  /**
   * 获取指定日期的状态
   * @param {Date} date - 日期
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @returns {string} 日期状态
   */
  getDateStatus(date, workId) {
    try {
      const dayData = this.getDayData(date, workId)
      return dayData.status || 'work'
    } catch (error) {
      console.error('获取日期状态失败:', error)
      return 'work'
    }
  }

  /**
   * 获取分类颜色配置
   * @param {string} category - 分类ID
   * @returns {Object} 颜色配置对象
   */
  getCategoryColorConfig(category) {
    const categoryColors = {
      work_status: {
        color: '#3B82F6',
        backgroundColor: '#EFF6FF',
        borderColor: '#3B82F6'
      },
      legal_holidays: {
        color: '#10B981',
        backgroundColor: '#F0FDF4',
        borderColor: '#10B981'
      },
      personal_leave: {
        color: '#F59E0B',
        backgroundColor: '#FFFBEB',
        borderColor: '#F59E0B'
      }
    }

    return categoryColors[category] || categoryColors.work_status
  }

  /**
   * 获取日期状态配置
   * @param {string} status - 状态值
   * @returns {Object} 状态配置对象
   */
  getDateStatusConfig(status) {
    // 动态从分类配置中生成状态配置
    const categories = this.getDateStatusCategories()
    const statusConfigs = {}

    // 遍历所有分类和类型，构建状态配置映射
    categories.forEach(category => {
      category.types.forEach(type => {
        statusConfigs[type.value] = {
          icon: type.icon,
          text: type.text,
          name: type.text,
          category: category.id
        }
      })
    })

    // 如果状态不存在，返回默认的工作日状态
    const baseConfig = statusConfigs[status] || statusConfigs.work || {
      icon: '💼',
      text: '工作日',
      name: '工作日',
      category: 'work_status'
    }

    const colorConfig = this.getCategoryColorConfig(baseConfig.category)

    return {
      ...baseConfig,
      ...colorConfig
    }
  }

  /**
   * 获取所有状态选项（从分类数据中提取）
   * @returns {Array} 状态选项数组
   */
  getStatusOptions() {
    const categories = this.getDateStatusCategories()
    const statusOptions = []

    // 从分类数据中提取所有启用的状态选项
    categories.forEach(category => {
      category.types.forEach(type => {
        if (type.enabled) {
          statusOptions.push({
            value: type.value,
            text: type.text,
            icon: type.icon,
            color: type.color,
            backgroundColor: type.backgroundColor,
            borderColor: type.borderColor
          })
        }
      })
    })

    return statusOptions
  }

  /**
   * 获取分类的日期状态选项
   * @returns {Array} 分类的状态选项数组
   */
  getDateStatusCategories() {
    const categories = [
      {
        id: 'work_status',
        name: '工作/出勤状态类',
        icon: '💼',
        description: '日常工作和出勤相关状态',
        types: [
          // 高频基础状态优先：工作日 > 常规休息 > 轮休（排班制常见）
          { value: 'work', text: '工作日', icon: '💼', enabled: true, description: '正常工作日' },
          { value: 'rest', text: '休息', icon: '😴', enabled: true, description: '常规休息日' },
          { value: 'rotation_rest', text: '轮休', icon: '🔄', enabled: true, description: '排班制轮流休息' },
          // 次高频/特定场景：补休 > 值班（比待岗、停工更常见）
          { value: 'compensatory_rest', text: '补休', icon: '⏰', enabled: true, description: '补偿性休息' },
          { value: 'duty', text: '值班', icon: '🛡️', enabled: true, description: '值班时间' },
          // 低频特殊状态：待岗 > 停工留薪期（工伤场景较少）
          { value: 'standby', text: '待岗', icon: '⏳', enabled: true, description: '待岗状态' },
          { value: 'work_suspension', text: '停工留薪期', icon: '⏸️', enabled: true, description: '工伤停工留薪期' }
          // 无状态已移除，只要一个日期有数据就应该设置为其他的任意状态
        ]
      },
      {
        id: 'legal_holidays',
        name: '法定/特殊假日类',
        icon: '🎉',
        description: '国家法定假日和特殊假期',
        types: [
          // 高频法定场景：公休日（周末）> 法定节假日 > 调休（因法定假产生）
          { value: 'weekend', text: '公休日', icon: '🏖️', enabled: true, description: '周末公休日' },
          { value: 'holiday', text: '法定节假日', icon: '🎉', enabled: true, description: '国家法定节假日' },
          { value: 'adjusted_leave', text: '调休日', icon: '🔄', enabled: true, description: '节假日调休' },
          // 次高频特殊假期：带薪年休假 > 节日假（特定节日才有的假期）
          { value: 'annual_leave', text: '带薪年休假', icon: '🌴', enabled: true, description: '年度带薪假期' },
          { value: 'festival_leave', text: '节日假', icon: '🎊', enabled: true, description: '特定节日假期' }
        ]
      },
      {
        id: 'personal_leave',
        name: '请假/缺勤类',
        icon: '🏠',
        description: '个人请假和缺勤相关状态',
        types: [
          // 高频个人请假：事假 > 病假（最常见的两类个人请假）
          { value: 'leave', text: '事假', icon: '🏠', enabled: true, description: '个人事务请假' },
          { value: 'sick', text: '病假', icon: '🤒', enabled: true, description: '因病请假' },
          // 次高频人生事件假：婚假 > 丧假（比产假、陪产假更普适）
          { value: 'marriage_leave', text: '婚假', icon: '💒', enabled: true, description: '结婚假期' },
          { value: 'bereavement_leave', text: '丧假', icon: '🕯️', enabled: true, description: '亲属丧假' },
          // 特定人群假：产假 > 陪产假（适用范围相对较窄）
          { value: 'maternity_leave', text: '产假', icon: '👶', enabled: true, description: '女性产假' },
          { value: 'paternity_leave', text: '陪产假', icon: '👨‍👶', enabled: true, description: '男性陪产假' },
          // 低频特殊假：探亲假（需满足异地等条件）> 工伤假（因工伤产生）
          { value: 'family_visit_leave', text: '探亲假', icon: '👨‍👩‍👧‍👦', enabled: true, description: '探望亲属假' },
          { value: 'work_injury_leave', text: '工伤假', icon: '🏥', enabled: true, description: '工伤治疗假' },
          // 异常状态放最后：旷工（负面状态，使用频率低且需谨慎标记）
          { value: 'absent', text: '旷工', icon: '❌', enabled: true, description: '无故缺勤' }
        ]
      }
    ]

    // 为每个类型添加颜色配置信息（避免循环依赖）
    categories.forEach(category => {
      const colorConfig = this.getCategoryColorConfig(category.id)
      category.types.forEach(type => {
        // 直接添加颜色配置，避免调用 getDateStatusConfig 造成循环依赖
        Object.assign(type, colorConfig)
      })
    })

    return categories
  }

  /**
   * 设置指定日期的状态
   * @param {Date} date - 日期
   * @param {string} status - 状态
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   */
  setDateStatus(date, status, workId) {
    try {
      const dayData = this.getDayData(date, workId)
      dayData.status = status
      dayData.updateTime = new Date()
      this.saveDayData(date, dayData, workId)
    } catch (error) {
      console.error('设置日期状态失败:', error)
      throw new Error('设置日期状态失败')
    }
  }

  /**
   * 导出数据
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @returns {string} JSON格式的数据字符串
   */
  exportData(workId) {
    try {
      const timeTrackingData = this.getAllDailyData(workId)
      const exportData = {
        version: '0.2.0',
        exportTime: new Date().toISOString(),
        workId,
        timeTracking: timeTrackingData
      }
      
      return JSON.stringify(exportData, null, 2)
    } catch (error) {
      console.error('导出数据失败:', error)
      throw new Error('导出数据失败')
    }
  }

  /**
   * 导入数据
   * @param {string} jsonData - JSON格式的数据字符串
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @param {boolean} merge - 是否合并现有数据（true）还是覆盖（false）
   */
  importData(jsonData, workId, merge = false) {
    try {
      const importData = JSON.parse(jsonData)
      
      if (!importData.timeTracking || typeof importData.timeTracking !== 'object') {
        throw new Error('导入数据格式无效')
      }
      
      // 如果没有指定工作履历ID，使用当前工作
      if (!workId) {
        const currentWork = this.dataManager.getCurrentWork()
        if (!currentWork) {
          throw new Error('没有可用的工作履历ID')
        }
        workId = currentWork.id
      }
      
      let timeTracking = merge ? this.getAllDailyData(workId) : {}
      
      // 合并导入的数据
      Object.keys(importData.timeTracking).forEach(dateKey => {
        const dayData = importData.timeTracking[dateKey]
        
        // 处理日期字段
        if (dayData.workDate) dayData.workDate = new Date(dayData.workDate)
        if (dayData.createTime) dayData.createTime = new Date(dayData.createTime)
        if (dayData.updateTime) dayData.updateTime = new Date(dayData.updateTime)
        
        // 处理时间段
        if (dayData.segments) {
          dayData.segments = dayData.segments.map(segment => ({
            ...segment,
            start: new Date(segment.start),
            end: new Date(segment.end),
            createTime: segment.createTime ? new Date(segment.createTime) : new Date(),
            updateTime: segment.updateTime ? new Date(segment.updateTime) : new Date()
          }))
        }
        
        timeTracking[dateKey] = dayData
      })
      
      // 更新工作履历中的时间追踪数据
      this.dataManager.updateWork(workId, { timeTracking })
      
      const importedCount = Object.keys(importData.timeTracking).length
      return importedCount
    } catch (error) {
      console.error('导入数据失败:', error)
      throw new Error('导入数据失败: ' + error.message)
    }
  }

  /**
   * 清除所有数据
   * @param {string} workId - 工作履历ID，为空则清除当前工作的数据
   */
  clearAllData(workId) {
    try {
      // 如果没有指定工作履历ID，使用当前工作
      if (!workId) {
        const currentWork = this.dataManager.getCurrentWork()
        if (!currentWork) {
          throw new Error('没有可用的工作履历ID')
        }
        workId = currentWork.id
      }
      
      // 清空该工作的时间追踪数据
      this.dataManager.updateWork(workId, { timeTracking: {} })
      
      console.log('时间段数据已清除')
    } catch (error) {
      console.error('清除时间段数据失败:', error)
      throw new Error('清除数据失败')
    }
  }

  /**
   * 添加数据变化监听器
   * @param {Function} listener - 监听器函数
   */
  addChangeListener(listener) {
    this.dataManager.addChangeListener(listener)
  }

  /**
   * 移除数据变化监听器
   * @param {Function} listener - 监听器函数
   */
  removeChangeListener(listener) {
    this.dataManager.removeChangeListener(listener)
  }

  /**
   * 清理日期数据用于存储
   * @param {Object} dayData - 日期数据
   * @returns {Object} 清理后的日期数据
   */
  cleanDayDataForStorage(dayData) {
    if (!dayData) return dayData

    // 清理时间段数据，只保留最核心的字段，移除所有冗余字段
    const cleanedSegments = (dayData.segments || []).map((segment, index) => {
      return {
        id: index,                      // 简化：使用递增数字ID
        start: segment.start,           // 核心：开始时间（分钟数）
        end: segment.end,               // 核心：结束时间（分钟数）
        type: segment.type,             // 核心：时间段类型
        income: segment.income || 0     // 核心：收入
        // 移除：hourlyRate（可计算）, createTime, updateTime（不必要）
        // 移除：startTime, endTime, duration, durationMinutes, typeText（可动态计算）
      }
    })

    // 清理摸鱼数据，只保留核心字段
    const cleanedFishes = (dayData.fishes || []).map((fish, index) => {
      return {
        id: fish.id !== undefined ? fish.id : index, // 保持原有ID或使用索引
        start: fish.start,                           // 核心：开始时间（分钟数）
        end: fish.end,                               // 核心：结束时间（分钟数）
        remark: fish.remark || ''                    // 核心：备注
        // 移除：startTime, endTime, duration等可计算字段
      }
    })

    // 清理日期数据，只保留核心字段
    return {
      date: dayData.date,
      segments: cleanedSegments,
      fishes: cleanedFishes,  // 保留摸鱼数据
      status: dayData.status
    }
  }

  /**
   * 动态计算日收入
   * @param {Array} segments - 时间段数组
   * @returns {number} 日收入总额
   */
  calculateDailyIncome(segments) {
    if (!Array.isArray(segments)) return 0

    return segments
      .filter(segment => segment.type !== 'rest')
      .reduce((total, segment) => total + (segment.income || 0), 0)
  }

  /**
   * 动态计算工作时长（分钟）
   * @param {Array} segments - 时间段数组
   * @returns {number} 工作时长（分钟）
   */
  calculateWorkMinutes(segments) {
    if (!Array.isArray(segments)) return 0

    return segments
      .filter(segment => segment.type !== 'rest')
      .reduce((total, segment) => total + (segment.end - segment.start), 0)
  }

  /**
   * 动态计算平均时薪
   * @param {Array} segments - 时间段数组
   * @returns {number} 平均时薪
   */
  calculateAverageHourlyRate(segments) {
    const dailyIncome = this.calculateDailyIncome(segments)
    const workMinutes = this.calculateWorkMinutes(segments)

    if (workMinutes === 0) return 0

    return dailyIncome / (workMinutes / 60)
  }

  /**
   * 从时间段数组计算统计信息
   * @param {Array} segments - 时间段数组
   * @returns {Object} 统计信息
   */
  calculateStatsFromSegments(segments) {
    if (!Array.isArray(segments)) {
      return {
        totalWorkMinutes: 0,
        totalIncome: 0,
        averageHourlyRate: 0
      }
    }

    let totalWorkMinutes = 0
    let totalIncome = 0

    segments.forEach(segment => {
      if (segment.type !== 'rest') {
        const duration = segment.end - segment.start
        totalWorkMinutes += duration
        totalIncome += segment.income || 0
      }
    })

    const averageHourlyRate = totalWorkMinutes > 0 ? totalIncome / (totalWorkMinutes / 60) : 0

    return {
      totalWorkMinutes,
      totalIncome,
      averageHourlyRate
    }
  }

  /**
   * 检查摸鱼数据与时间段的冲突
   * @param {Array} fishes - 摸鱼数据数组
   * @param {Array} segments - 时间段数组
   * @returns {Object} 冲突检查结果
   */
  checkFishingConflictsWithSegments(fishes, segments) {
    if (!fishes || fishes.length === 0) {
      return { hasConflicts: false, conflicts: [] }
    }

    const conflicts = []

    fishes.forEach(fish => {
      // 检查摸鱼时间是否还在某个工作时间段内
      const containingSegment = segments.find(segment => {
        return segment.type !== 'rest' &&
               fish.start >= segment.start &&
               fish.end <= segment.end
      })

      if (!containingSegment) {
        // 查找与摸鱼时间有重叠的工作时间段
        const overlappingSegments = segments.filter(segment => {
          return segment.type !== 'rest' &&
                 !(fish.end <= segment.start || fish.start >= segment.end)
        })

        let reason = '摸鱼时间不在任何工作时间段内'
        if (overlappingSegments.length > 0) {
          reason = '摸鱼时间与工作时间段部分重叠，需要完全包含在工作时间段内'
        }

        conflicts.push({
          fishingId: fish.id,
          fishingTime: `${minutesToTimeDisplay(fish.start)} - ${minutesToTimeDisplay(fish.end)}`,
          fishingRemark: fish.remark || '',
          reason: reason,
          overlappingSegments: overlappingSegments.map(seg => ({
            start: seg.start,
            end: seg.end,
            type: seg.type,
            timeRange: `${minutesToTimeDisplay(seg.start)} - ${minutesToTimeDisplay(seg.end)}`
          }))
        })
      }
    })

    return {
      hasConflicts: conflicts.length > 0,
      conflicts: conflicts
    }
  }
}

module.exports = {
  TimeSegmentService
}