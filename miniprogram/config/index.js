/**
 * 配置统一入口
 * 提供所有配置的统一访问接口
 */

// 业务配置
const DateStatusConfig = require('./business/date-status')
const IncomeAdjustmentConfig = require('./business/income-adjustment')
const MembershipConfig = require('./business/membership')

// 界面配置
const ThemesConfig = require('./ui/themes')
const DashboardConfig = require('./ui/dashboard')

// 应用配置
const Constants = require('./app/constants')

/**
 * 配置管理器
 */
class ConfigManager {
  constructor() {
    this.configs = {
      // 业务配置
      dateStatus: DateStatusConfig,
      incomeAdjustment: IncomeAdjustmentConfig,
      membership: MembershipConfig,
      
      // 界面配置
      themes: ThemesConfig,
      dashboard: DashboardConfig,
      
      // 应用配置
      constants: Constants
    }
  }

  /**
   * 获取日期状态配置
   * @returns {Object} 日期状态配置
   */
  getDateStatusConfig() {
    return this.configs.dateStatus
  }

  /**
   * 获取收入调整配置
   * @returns {Object} 收入调整配置
   */
  getIncomeAdjustmentConfig() {
    return this.configs.incomeAdjustment
  }

  /**
   * 获取会员配置
   * @returns {Object} 会员配置
   */
  getMembershipConfig() {
    return this.configs.membership
  }

  /**
   * 获取主题配置
   * @returns {Object} 主题配置
   */
  getThemesConfig() {
    return this.configs.themes
  }

  /**
   * 获取仪表盘配置
   * @returns {Object} 仪表盘配置
   */
  getDashboardConfig() {
    return this.configs.dashboard
  }

  /**
   * 获取常量配置
   * @returns {Object} 常量配置
   */
  getConstants() {
    return this.configs.constants
  }

  /**
   * 获取指定配置
   * @param {string} configName - 配置名称
   * @returns {Object|null} 配置对象
   */
  getConfig(configName) {
    return this.configs[configName] || null
  }

  /**
   * 获取所有配置
   * @returns {Object} 所有配置
   */
  getAllConfigs() {
    return this.configs
  }

  /**
   * 检查配置是否存在
   * @param {string} configName - 配置名称
   * @returns {boolean} 是否存在
   */
  hasConfig(configName) {
    return this.configs.hasOwnProperty(configName)
  }
}

// 创建全局配置管理器实例
const configManager = new ConfigManager()

/**
 * 获取配置管理器实例
 * @returns {ConfigManager} 配置管理器
 */
function getConfigManager() {
  return configManager
}

// 导出常用配置的快捷访问方法
module.exports = {
  // 配置管理器
  ConfigManager,
  getConfigManager,
  
  // 业务配置快捷访问
  DateStatus: DateStatusConfig,
  IncomeAdjustment: IncomeAdjustmentConfig,
  Membership: MembershipConfig,
  
  // 界面配置快捷访问
  Themes: ThemesConfig,
  Dashboard: DashboardConfig,
  
  // 应用配置快捷访问
  Constants: Constants,
  
  // 常用方法快捷访问
  getDateStatusCategories: DateStatusConfig.getDateStatusCategories,
  getCategoryColorConfig: DateStatusConfig.getCategoryColorConfig,
  getThemeConfig: ThemesConfig.getThemeConfig,
  getDashboardType: DashboardConfig.getDashboardType,
  getStorageKey: Constants.getStorageKey,
  
  // 验证方法快捷访问
  isValidStatus: DateStatusConfig.isValidStatus,
  isValidTheme: ThemesConfig.isValidTheme,
  isValidDashboard: DashboardConfig.isValidDashboard,
  hasFeaturePermission: MembershipConfig.hasFeaturePermission
}
