/**
 * 仪表盘配置
 * 定义不同仪表盘类型和默认配置
 */

/**
 * 仪表盘类型定义
 */
const DASHBOARD_TYPES = {
  dashboard1: {
    id: 'dashboard1',
    name: '经典仪表盘',
    description: '传统的时间跟踪界面，功能全面',
    icon: '📊',
    component: 'dashboard1',
    isDefault: true,
    // 默认配置
    defaultConfig: {
      showRealTimeIncome: true,     // 显示实时收入
      showQuickActions: true,       // 显示快捷操作
      chartHeight: 120,             // 图表高度
      showCurrentWork: true,        // 显示当前工作
      incomeDecimalPlaces: 3        // 收入小数位数
    }
  },
  
  dashboard2: {
    id: 'dashboard2',
    name: '现代仪表盘',
    description: '现代化设计，卡片式布局',
    icon: '🎯',
    component: 'dashboard2',
    isDefault: false,
    // 默认配置
    defaultConfig: {
      showCountdown: true,          // 显示倒计时
      showAllStats: true,           // 显示所有统计
      circularProgressSize: 400,    // 圆形进度条大小
      showCurrentWork: true,        // 显示当前工作
      incomeDecimalPlaces: 3        // 收入小数位数
    }
  }
}

/**
 * 默认仪表盘设置
 */
const DEFAULT_DASHBOARD_SETTINGS = {
  // 当前选择的仪表盘ID
  currentDashboard: 'dashboard1',
  
  // 是否首次使用
  isFirstTime: false,
  
  // 各仪表盘的个性化配置
  configs: {
    dashboard1: {
      showRealTimeIncome: true,
      showQuickActions: true,
      chartHeight: 120,
      showCurrentWork: true,
      incomeDecimalPlaces: 3
    },
    dashboard2: {
      showCountdown: true,
      showAllStats: true,
      circularProgressSize: 400,
      showCurrentWork: true,
      incomeDecimalPlaces: 3
    }
  }
}

/**
 * 仪表盘配置选项定义
 */
const DASHBOARD_CONFIG_OPTIONS = {
  dashboard1: [
    {
      key: 'showRealTimeIncome',
      name: '显示实时收入',
      type: 'boolean',
      default: true,
      description: '在仪表盘上显示实时收入信息'
    },
    {
      key: 'showQuickActions',
      name: '显示快捷操作',
      type: 'boolean',
      default: true,
      description: '显示常用的快捷操作按钮'
    },
    {
      key: 'chartHeight',
      name: '图表高度',
      type: 'number',
      default: 120,
      min: 80,
      max: 200,
      description: '时间图表的显示高度'
    },
    {
      key: 'showCurrentWork',
      name: '显示当前工作',
      type: 'boolean',
      default: true,
      description: '显示当前工作履历信息'
    },
    {
      key: 'incomeDecimalPlaces',
      name: '收入小数位数',
      type: 'number',
      default: 3,
      min: 0,
      max: 6,
      description: '收入显示的小数位数'
    }
  ],
  
  dashboard2: [
    {
      key: 'showCountdown',
      name: '显示倒计时',
      type: 'boolean',
      default: true,
      description: '显示工作时间倒计时'
    },
    {
      key: 'showAllStats',
      name: '显示所有统计',
      type: 'boolean',
      default: true,
      description: '显示详细的统计信息'
    },
    {
      key: 'circularProgressSize',
      name: '圆形进度条大小',
      type: 'number',
      default: 400,
      min: 200,
      max: 600,
      description: '圆形进度条的显示大小'
    },
    {
      key: 'showCurrentWork',
      name: '显示当前工作',
      type: 'boolean',
      default: true,
      description: '显示当前工作履历信息'
    },
    {
      key: 'incomeDecimalPlaces',
      name: '收入小数位数',
      type: 'number',
      default: 3,
      min: 0,
      max: 6,
      description: '收入显示的小数位数'
    }
  ]
}

/**
 * 获取所有仪表盘类型
 * @returns {Object} 仪表盘类型对象
 */
function getAllDashboardTypes() {
  return JSON.parse(JSON.stringify(DASHBOARD_TYPES))
}

/**
 * 获取仪表盘类型列表
 * @returns {Array} 仪表盘类型数组
 */
function getDashboardTypeList() {
  return Object.values(DASHBOARD_TYPES).map(dashboard => ({
    id: dashboard.id,
    name: dashboard.name,
    description: dashboard.description,
    icon: dashboard.icon,
    isDefault: dashboard.isDefault
  }))
}

/**
 * 获取仪表盘类型配置
 * @param {string} dashboardId - 仪表盘ID
 * @returns {Object|null} 仪表盘类型配置
 */
function getDashboardType(dashboardId) {
  return DASHBOARD_TYPES[dashboardId] ? 
    JSON.parse(JSON.stringify(DASHBOARD_TYPES[dashboardId])) : null
}

/**
 * 获取默认仪表盘设置
 * @returns {Object} 默认设置
 */
function getDefaultDashboardSettings() {
  return JSON.parse(JSON.stringify(DEFAULT_DASHBOARD_SETTINGS))
}

/**
 * 获取仪表盘配置选项
 * @param {string} dashboardId - 仪表盘ID
 * @returns {Array} 配置选项数组
 */
function getDashboardConfigOptions(dashboardId) {
  return DASHBOARD_CONFIG_OPTIONS[dashboardId] ? 
    JSON.parse(JSON.stringify(DASHBOARD_CONFIG_OPTIONS[dashboardId])) : []
}

/**
 * 检查仪表盘是否存在
 * @param {string} dashboardId - 仪表盘ID
 * @returns {boolean} 是否存在
 */
function isValidDashboard(dashboardId) {
  return DASHBOARD_TYPES.hasOwnProperty(dashboardId)
}

/**
 * 获取默认仪表盘ID
 * @returns {string} 默认仪表盘ID
 */
function getDefaultDashboardId() {
  const defaultDashboard = Object.values(DASHBOARD_TYPES).find(d => d.isDefault)
  return defaultDashboard ? defaultDashboard.id : 'dashboard1'
}

/**
 * 验证仪表盘配置
 * @param {string} dashboardId - 仪表盘ID
 * @param {Object} config - 配置对象
 * @returns {Object} 验证结果
 */
function validateDashboardConfig(dashboardId, config) {
  const options = getDashboardConfigOptions(dashboardId)
  const errors = []
  
  for (const option of options) {
    const value = config[option.key]
    
    if (value !== undefined) {
      if (option.type === 'number') {
        if (typeof value !== 'number' || isNaN(value)) {
          errors.push(`${option.name} 必须是有效数字`)
        } else if (option.min !== undefined && value < option.min) {
          errors.push(`${option.name} 不能小于 ${option.min}`)
        } else if (option.max !== undefined && value > option.max) {
          errors.push(`${option.name} 不能大于 ${option.max}`)
        }
      } else if (option.type === 'boolean') {
        if (typeof value !== 'boolean') {
          errors.push(`${option.name} 必须是布尔值`)
        }
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

module.exports = {
  DASHBOARD_TYPES,
  DEFAULT_DASHBOARD_SETTINGS,
  DASHBOARD_CONFIG_OPTIONS,
  getAllDashboardTypes,
  getDashboardTypeList,
  getDashboardType,
  getDefaultDashboardSettings,
  getDashboardConfigOptions,
  isValidDashboard,
  getDefaultDashboardId,
  validateDashboardConfig
}
