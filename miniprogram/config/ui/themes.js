/**
 * 主题配置
 * 定义应用的主题样式和颜色方案
 */

/**
 * 主题定义
 */
const THEMES = {
  default: {
    id: 'default',
    name: '默认主题',
    description: '清新简洁的默认主题',
    icon: '🎨',
    colors: {
      primary: '#4F46E5',           // 主色调
      secondary: '#6366F1',         // 次要色调
      accent: '#8B5CF6',            // 强调色
      background: '#F8FAFC',        // 背景色
      surface: '#FFFFFF',           // 表面色
      text: {
        primary: '#1F2937',         // 主要文字
        secondary: '#6B7280',       // 次要文字
        disabled: '#9CA3AF'         // 禁用文字
      },
      border: '#E5E7EB',            // 边框色
      divider: '#F3F4F6',           // 分割线
      success: '#10B981',           // 成功色
      warning: '#F59E0B',           // 警告色
      error: '#EF4444',             // 错误色
      info: '#3B82F6'               // 信息色
    }
  },
  
  dark: {
    id: 'dark',
    name: '深色主题',
    description: '护眼的深色主题',
    icon: '🌙',
    colors: {
      primary: '#6366F1',
      secondary: '#8B5CF6',
      accent: '#A855F7',
      background: '#111827',
      surface: '#1F2937',
      text: {
        primary: '#F9FAFB',
        secondary: '#D1D5DB',
        disabled: '#6B7280'
      },
      border: '#374151',
      divider: '#2D3748',
      success: '#10B981',
      warning: '#F59E0B',
      error: '#EF4444',
      info: '#3B82F6'
    }
  },
  
  blue: {
    id: 'blue',
    name: '蓝色主题',
    description: '专业的蓝色商务主题',
    icon: '💙',
    colors: {
      primary: '#2563EB',
      secondary: '#3B82F6',
      accent: '#1D4ED8',
      background: '#F8FAFC',
      surface: '#FFFFFF',
      text: {
        primary: '#1E293B',
        secondary: '#64748B',
        disabled: '#94A3B8'
      },
      border: '#E2E8F0',
      divider: '#F1F5F9',
      success: '#059669',
      warning: '#D97706',
      error: '#DC2626',
      info: '#0284C7'
    }
  },
  
  green: {
    id: 'green',
    name: '绿色主题',
    description: '清新的绿色自然主题',
    icon: '💚',
    colors: {
      primary: '#059669',
      secondary: '#10B981',
      accent: '#047857',
      background: '#F8FAFC',
      surface: '#FFFFFF',
      text: {
        primary: '#1F2937',
        secondary: '#6B7280',
        disabled: '#9CA3AF'
      },
      border: '#E5E7EB',
      divider: '#F3F4F6',
      success: '#10B981',
      warning: '#F59E0B',
      error: '#EF4444',
      info: '#3B82F6'
    }
  }
}

/**
 * 默认主题ID
 */
const DEFAULT_THEME = 'default'

/**
 * 获取所有主题
 * @returns {Object} 主题对象
 */
function getAllThemes() {
  return JSON.parse(JSON.stringify(THEMES))
}

/**
 * 获取主题列表
 * @returns {Array} 主题数组
 */
function getThemeList() {
  return Object.values(THEMES).map(theme => ({
    id: theme.id,
    name: theme.name,
    description: theme.description,
    icon: theme.icon
  }))
}

/**
 * 获取主题配置
 * @param {string} themeId - 主题ID
 * @returns {Object} 主题配置
 */
function getThemeConfig(themeId = DEFAULT_THEME) {
  return JSON.parse(JSON.stringify(THEMES[themeId] || THEMES[DEFAULT_THEME]))
}

/**
 * 检查主题是否存在
 * @param {string} themeId - 主题ID
 * @returns {boolean} 是否存在
 */
function isValidTheme(themeId) {
  return THEMES.hasOwnProperty(themeId)
}

/**
 * 获取主题颜色
 * @param {string} themeId - 主题ID
 * @param {string} colorKey - 颜色键名
 * @returns {string} 颜色值
 */
function getThemeColor(themeId, colorKey) {
  const theme = getThemeConfig(themeId)
  const keys = colorKey.split('.')
  let color = theme.colors
  
  for (const key of keys) {
    if (color && typeof color === 'object') {
      color = color[key]
    } else {
      return null
    }
  }
  
  return typeof color === 'string' ? color : null
}

/**
 * 生成CSS变量
 * @param {string} themeId - 主题ID
 * @returns {Object} CSS变量对象
 */
function generateCSSVariables(themeId) {
  const theme = getThemeConfig(themeId)
  const variables = {}
  
  function flattenColors(colors, prefix = '--color') {
    for (const [key, value] of Object.entries(colors)) {
      if (typeof value === 'string') {
        variables[`${prefix}-${key}`] = value
      } else if (typeof value === 'object') {
        flattenColors(value, `${prefix}-${key}`)
      }
    }
  }
  
  flattenColors(theme.colors)
  return variables
}

module.exports = {
  THEMES,
  DEFAULT_THEME,
  getAllThemes,
  getThemeList,
  getThemeConfig,
  isValidTheme,
  getThemeColor,
  generateCSSVariables
}
