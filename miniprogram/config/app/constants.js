/**
 * 应用常量配置
 * 定义应用中使用的各种常量
 */

/**
 * 应用信息
 */
const APP_INFO = {
  name: '工时记录',
  version: '1.0.0',
  description: '专业的工时记录和收入统计工具',
  author: '开发团队',
  website: 'https://example.com'
}

/**
 * 存储键名常量
 */
const STORAGE_KEYS = {
  // 用户数据
  USER_INFO: 'userInfo',
  USER_SETTINGS: 'userSettings',
  
  // 工作数据
  WORK_HISTORIES: 'workHistories',
  TIME_TRACKING: 'timeTracking',
  CURRENT_WORK_ID: 'currentWorkId',
  
  // 应用设置
  THEME_ID: 'themeId',
  DASHBOARD_TYPE: 'dashboardType',
  DASHBOARD_SETTINGS: 'dashboardSettings',
  
  // 缓存数据
  CACHE_DATA: 'cacheData',
  LAST_SYNC_TIME: 'lastSyncTime',
  
  // 临时数据
  TEMP_DATA: 'tempData',
  DRAFT_DATA: 'draftData'
}

/**
 * 时间相关常量
 */
const TIME_CONSTANTS = {
  // 时间单位（分钟）
  MINUTE: 1,
  HOUR: 60,
  DAY: 1440,
  WEEK: 10080,
  MONTH: 43200,  // 30天
  
  // 时间格式
  TIME_FORMAT: 'HH:mm',
  DATE_FORMAT: 'YYYY-MM-DD',
  DATETIME_FORMAT: 'YYYY-MM-DD HH:mm:ss',
  
  // 工作时间范围
  MIN_WORK_HOUR: 0,      // 最早工作时间（小时）
  MAX_WORK_HOUR: 24,     // 最晚工作时间（小时）
  DEFAULT_WORK_START: 9, // 默认开始工作时间
  DEFAULT_WORK_END: 18,  // 默认结束工作时间
  
  // 时间段限制
  MIN_SEGMENT_DURATION: 1,    // 最小时间段长度（分钟）
  MAX_SEGMENT_DURATION: 1440, // 最大时间段长度（分钟，24小时）
  
  // 摸鱼时间限制
  MIN_FISH_DURATION: 1,       // 最小摸鱼时长（分钟）
  MAX_FISH_DURATION: 480      // 最大摸鱼时长（分钟，8小时）
}

/**
 * 数据限制常量
 */
const DATA_LIMITS = {
  // 文本长度限制
  MAX_WORK_NAME_LENGTH: 50,        // 工作名称最大长度
  MAX_COMPANY_NAME_LENGTH: 100,    // 公司名称最大长度
  MAX_DESCRIPTION_LENGTH: 500,     // 描述最大长度
  MAX_NOTE_LENGTH: 200,            // 备注最大长度
  
  // 数值限制
  MAX_HOURLY_RATE: 9999.99,        // 最大时薪
  MIN_HOURLY_RATE: 0.01,           // 最小时薪
  MAX_DAILY_INCOME: 99999.99,      // 最大日收入
  
  // 数组长度限制
  MAX_SEGMENTS_PER_DAY: 50,        // 每日最大时间段数
  MAX_FISH_RECORDS_PER_DAY: 20,    // 每日最大摸鱼记录数
  MAX_INCOME_ITEMS_PER_DAY: 10,    // 每日最大收入调整项数
  
  // 历史数据限制
  MAX_WORK_HISTORIES: 20,          // 最大工作履历数
  MAX_DATA_RETENTION_DAYS: 3650    // 最大数据保留天数（10年）
}

/**
 * 网络相关常量
 */
const NETWORK_CONSTANTS = {
  // 请求超时时间（毫秒）
  REQUEST_TIMEOUT: 10000,
  
  // 重试次数
  MAX_RETRY_COUNT: 3,
  
  // 缓存时间（毫秒）
  CACHE_DURATION: 300000,  // 5分钟
  
  // 同步间隔（毫秒）
  SYNC_INTERVAL: 600000    // 10分钟
}

/**
 * 界面相关常量
 */
const UI_CONSTANTS = {
  // 动画时长（毫秒）
  ANIMATION_DURATION: 300,
  
  // 防抖延迟（毫秒）
  DEBOUNCE_DELAY: 500,
  
  // 节流间隔（毫秒）
  THROTTLE_INTERVAL: 100,
  
  // 页面大小
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  
  // 颜色透明度
  OVERLAY_OPACITY: 0.5,
  DISABLED_OPACITY: 0.6
}

/**
 * 错误代码常量
 */
const ERROR_CODES = {
  // 通用错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  
  // 数据错误
  DATA_NOT_FOUND: 'DATA_NOT_FOUND',
  DATA_INVALID: 'DATA_INVALID',
  DATA_EXPIRED: 'DATA_EXPIRED',
  
  // 权限错误
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  VIP_REQUIRED: 'VIP_REQUIRED',
  
  // 业务错误
  WORK_NOT_FOUND: 'WORK_NOT_FOUND',
  TIME_CONFLICT: 'TIME_CONFLICT',
  INVALID_TIME_RANGE: 'INVALID_TIME_RANGE'
}

/**
 * 事件名称常量
 */
const EVENT_NAMES = {
  // 数据变更事件
  DATA_CHANGED: 'dataChanged',
  WORK_CHANGED: 'workChanged',
  SETTINGS_CHANGED: 'settingsChanged',
  
  // 用户操作事件
  USER_LOGIN: 'userLogin',
  USER_LOGOUT: 'userLogout',
  
  // 应用状态事件
  APP_SHOW: 'appShow',
  APP_HIDE: 'appHide',
  
  // 同步事件
  SYNC_START: 'syncStart',
  SYNC_SUCCESS: 'syncSuccess',
  SYNC_ERROR: 'syncError'
}

/**
 * 获取应用信息
 * @returns {Object} 应用信息
 */
function getAppInfo() {
  return JSON.parse(JSON.stringify(APP_INFO))
}

/**
 * 获取存储键名
 * @param {string} key - 键名
 * @returns {string} 完整的存储键名
 */
function getStorageKey(key) {
  return STORAGE_KEYS[key] || key
}

/**
 * 检查是否为有效的错误代码
 * @param {string} code - 错误代码
 * @returns {boolean} 是否有效
 */
function isValidErrorCode(code) {
  return Object.values(ERROR_CODES).includes(code)
}

module.exports = {
  APP_INFO,
  STORAGE_KEYS,
  TIME_CONSTANTS,
  DATA_LIMITS,
  NETWORK_CONSTANTS,
  UI_CONSTANTS,
  ERROR_CODES,
  EVENT_NAMES,
  getAppInfo,
  getStorageKey,
  isValidErrorCode
}
