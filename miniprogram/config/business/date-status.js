/**
 * 日期状态配置
 * 包含所有工作日期状态的定义和分类
 */

/**
 * 日期状态分类配置
 * 按业务逻辑和使用频率进行分类和排序
 */
const DATE_STATUS_CATEGORIES = [
  {
    id: 'work_status',
    name: '工作/出勤状态类',
    icon: '💼',
    description: '日常工作和出勤相关状态',
    types: [
      // 高频基础状态优先：工作日 > 常规休息 > 轮休（排班制常见）
      { value: 'work', text: '工作日', icon: '💼', enabled: true, description: '正常工作日' },
      { value: 'rest', text: '休息', icon: '😴', enabled: true, description: '常规休息日' },
      { value: 'rotation_rest', text: '轮休', icon: '🔄', enabled: true, description: '排班制轮流休息' },
      // 次高频/特定场景：补休 > 值班（比待岗、停工更常见）
      { value: 'compensatory_rest', text: '补休', icon: '⏰', enabled: true, description: '补偿性休息' },
      { value: 'duty', text: '值班', icon: '🛡️', enabled: true, description: '值班时间' },
      // 低频特殊状态：待岗 > 停工留薪期（工伤场景较少）
      { value: 'standby', text: '待岗', icon: '⏳', enabled: true, description: '待岗状态' },
      { value: 'work_suspension', text: '停工留薪期', icon: '⏸️', enabled: true, description: '工伤停工留薪期' }
    ]
  },
  {
    id: 'legal_holidays',
    name: '法定/特殊假日类',
    icon: '🎉',
    description: '国家法定假日和特殊假期',
    types: [
      // 高频法定场景：公休日（周末）> 法定节假日 > 调休（因法定假产生）
      { value: 'weekend', text: '公休日', icon: '🏖️', enabled: true, description: '周末公休日' },
      { value: 'holiday', text: '法定节假日', icon: '🎉', enabled: true, description: '国家法定节假日' },
      { value: 'adjusted_leave', text: '调休日', icon: '🔄', enabled: true, description: '节假日调休' },
      // 次高频特殊假期：带薪年休假 > 节日假（特定节日才有的假期）
      { value: 'annual_leave', text: '带薪年休假', icon: '🌴', enabled: true, description: '年度带薪假期' },
      { value: 'festival_leave', text: '节日假', icon: '🎊', enabled: true, description: '特定节日假期' }
    ]
  },
  {
    id: 'personal_leave',
    name: '请假/缺勤类',
    icon: '🏠',
    description: '个人请假和缺勤相关状态',
    types: [
      // 高频个人请假：事假 > 病假（最常见的两类个人请假）
      { value: 'leave', text: '事假', icon: '🏠', enabled: true, description: '个人事务请假' },
      { value: 'sick', text: '病假', icon: '🤒', enabled: true, description: '因病请假' },
      // 次高频人生事件假：婚假 > 丧假（比产假、陪产假更普适）
      { value: 'marriage_leave', text: '婚假', icon: '💒', enabled: true, description: '结婚假期' },
      { value: 'bereavement_leave', text: '丧假', icon: '🕯️', enabled: true, description: '亲属丧假' },
      // 特定人群假：产假 > 陪产假（适用范围相对较窄）
      { value: 'maternity_leave', text: '产假', icon: '👶', enabled: true, description: '女性产假' },
      { value: 'paternity_leave', text: '陪产假', icon: '👨‍👶', enabled: true, description: '男性陪产假' },
      // 低频特殊假：探亲假（需满足异地等条件）> 工伤假（因工伤产生）
      { value: 'family_visit_leave', text: '探亲假', icon: '👨‍👩‍👧‍👦', enabled: true, description: '探望亲属假' },
      { value: 'work_injury_leave', text: '工伤假', icon: '🏥', enabled: true, description: '工伤治疗假' },
      // 异常状态放最后：旷工（负面状态，使用频率低且需谨慎标记）
      { value: 'absent', text: '旷工', icon: '❌', enabled: true, description: '无故缺勤' }
    ]
  }
]

/**
 * 日期状态分类颜色配置
 * 每个分类对应一套颜色主题
 */
const DATE_STATUS_CATEGORY_COLORS = {
  work_status: {
    color: '#3B82F6',           // 蓝色文字
    backgroundColor: '#EFF6FF',  // 淡蓝色背景
    borderColor: '#3B82F6'      // 蓝色边框
  },
  legal_holidays: {
    color: '#10B981',           // 绿色文字
    backgroundColor: '#F0FDF4',  // 淡绿色背景
    borderColor: '#10B981'      // 绿色边框
  },
  personal_leave: {
    color: '#F59E0B',           // 橙色文字
    backgroundColor: '#FFFBEB',  // 淡橙色背景
    borderColor: '#F59E0B'      // 橙色边框
  }
}

/**
 * 默认状态配置
 */
const DEFAULT_STATUS = 'work'

/**
 * 获取所有日期状态分类
 * @returns {Array} 状态分类数组
 */
function getDateStatusCategories() {
  return JSON.parse(JSON.stringify(DATE_STATUS_CATEGORIES))
}

/**
 * 获取分类颜色配置
 * @param {string} categoryId - 分类ID
 * @returns {Object} 颜色配置
 */
function getCategoryColorConfig(categoryId) {
  return DATE_STATUS_CATEGORY_COLORS[categoryId] || DATE_STATUS_CATEGORY_COLORS.work_status
}

/**
 * 获取所有状态类型的扁平列表
 * @returns {Array} 状态类型数组
 */
function getAllStatusTypes() {
  const allTypes = []
  DATE_STATUS_CATEGORIES.forEach(category => {
    category.types.forEach(type => {
      allTypes.push({
        ...type,
        category: category.id,
        categoryName: category.name
      })
    })
  })
  return allTypes
}

/**
 * 根据状态值获取状态配置
 * @param {string} statusValue - 状态值
 * @returns {Object|null} 状态配置
 */
function getStatusConfig(statusValue) {
  const allTypes = getAllStatusTypes()
  return allTypes.find(type => type.value === statusValue) || null
}

/**
 * 检查状态是否存在
 * @param {string} statusValue - 状态值
 * @returns {boolean} 是否存在
 */
function isValidStatus(statusValue) {
  return getStatusConfig(statusValue) !== null
}

module.exports = {
  DATE_STATUS_CATEGORIES,
  DATE_STATUS_CATEGORY_COLORS,
  DEFAULT_STATUS,
  getDateStatusCategories,
  getCategoryColorConfig,
  getAllStatusTypes,
  getStatusConfig,
  isValidStatus
}
