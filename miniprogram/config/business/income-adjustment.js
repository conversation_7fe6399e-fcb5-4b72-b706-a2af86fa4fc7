/**
 * 收入调整配置
 * 包含额外收入和扣款的常见类型配置
 */

/**
 * 额外收入常见类型
 * 按使用频率排序
 */
const EXTRA_INCOME_TYPES = [
  '销售提成',
  '绩效奖金', 
  '加班费',
  '全勤奖',
  '交通补贴',
  '餐饮补贴',
  '项目奖金',
  '年终奖',
  '节日福利',
  '推荐奖励',
  '技能津贴',
  '夜班津贴',
  '高温津贴',
  '其他收入'
]

/**
 * 扣款常见类型
 * 按使用频率排序
 */
const DEDUCTION_TYPES = [
  '迟到扣款',
  '早退扣款',
  '缺勤扣款',
  '社保扣款',
  '公积金扣款',
  '个税扣款',
  '违规罚款',
  '工具损坏',
  '制服费用',
  '培训费用',
  '借款扣除',
  '其他扣款'
]

/**
 * 收入调整配置
 */
const INCOME_ADJUSTMENT_CONFIG = {
  // 金额输入限制
  amount: {
    min: 0.01,           // 最小金额
    max: 999999.99,      // 最大金额
    step: 0.01,          // 步进值
    decimalPlaces: 2     // 小数位数
  },
  
  // 描述输入限制
  description: {
    maxLength: 100,      // 最大长度
    placeholder: '请输入详细说明...'
  },
  
  // 类型输入限制
  type: {
    maxLength: 20,       // 最大长度
    placeholder: '请输入类型或选择常见类型'
  }
}

/**
 * 获取额外收入常见类型
 * @returns {Array} 类型数组
 */
function getExtraIncomeTypes() {
  return [...EXTRA_INCOME_TYPES]
}

/**
 * 获取扣款常见类型
 * @returns {Array} 类型数组
 */
function getDeductionTypes() {
  return [...DEDUCTION_TYPES]
}

/**
 * 获取收入调整配置
 * @returns {Object} 配置对象
 */
function getIncomeAdjustmentConfig() {
  return JSON.parse(JSON.stringify(INCOME_ADJUSTMENT_CONFIG))
}

/**
 * 验证金额是否有效
 * @param {number} amount - 金额
 * @returns {Object} 验证结果
 */
function validateAmount(amount) {
  const config = INCOME_ADJUSTMENT_CONFIG.amount
  
  if (typeof amount !== 'number' || isNaN(amount)) {
    return { isValid: false, error: '金额必须是有效数字' }
  }
  
  if (amount < config.min) {
    return { isValid: false, error: `金额不能小于 ${config.min}` }
  }
  
  if (amount > config.max) {
    return { isValid: false, error: `金额不能大于 ${config.max}` }
  }
  
  return { isValid: true }
}

/**
 * 验证类型是否有效
 * @param {string} type - 类型
 * @returns {Object} 验证结果
 */
function validateType(type) {
  const config = INCOME_ADJUSTMENT_CONFIG.type
  
  if (!type || typeof type !== 'string') {
    return { isValid: false, error: '类型不能为空' }
  }
  
  if (type.length > config.maxLength) {
    return { isValid: false, error: `类型长度不能超过 ${config.maxLength} 个字符` }
  }
  
  return { isValid: true }
}

/**
 * 验证描述是否有效
 * @param {string} description - 描述
 * @returns {Object} 验证结果
 */
function validateDescription(description) {
  const config = INCOME_ADJUSTMENT_CONFIG.description
  
  if (description && description.length > config.maxLength) {
    return { isValid: false, error: `描述长度不能超过 ${config.maxLength} 个字符` }
  }
  
  return { isValid: true }
}

/**
 * 格式化金额
 * @param {number} amount - 金额
 * @returns {number} 格式化后的金额
 */
function formatAmount(amount) {
  const decimals = INCOME_ADJUSTMENT_CONFIG.amount.decimalPlaces
  return Math.round(amount * Math.pow(10, decimals)) / Math.pow(10, decimals)
}

module.exports = {
  EXTRA_INCOME_TYPES,
  DEDUCTION_TYPES,
  INCOME_ADJUSTMENT_CONFIG,
  getExtraIncomeTypes,
  getDeductionTypes,
  getIncomeAdjustmentConfig,
  validateAmount,
  validateType,
  validateDescription,
  formatAmount
}
