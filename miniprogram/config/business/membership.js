/**
 * 会员权限配置
 * 定义免费用户和VIP用户的功能权限和使用限制
 */

/**
 * 免费功能列表
 * 这些功能对所有用户开放
 */
const FREE_FEATURES = [
  'export_data',           // 数据导出
  'basic_tracking',        // 基础时间跟踪
  'calendar_view',         // 日历查看
  'basic_statistics'       // 基础统计
]

/**
 * VIP专享功能列表
 * 这些功能只对VIP用户开放
 */
const VIP_FEATURES = [
  'data_sync',            // 数据同步
  'advanced_analytics',   // 高级分析
  'history_data',         // 历史数据
  'custom_themes',        // 自定义主题
  'batch_operations',     // 批量操作
  'advanced_export',      // 高级导出
  'priority_support'      // 优先支持
]

/**
 * 使用限制配置
 */
const USAGE_LIMITS = {
  // 免费用户限制
  free: {
    work_histories: 5,           // 工作履历数量
    time_segments_per_day: 10,   // 每日时间段数量
    data_retention_days: 30,     // 数据保留天数
    export_per_month: 3,         // 每月导出次数
    fish_records_per_day: 5      // 每日摸鱼记录数量
  },
  
  // VIP用户限制（-1 表示无限制）
  vip: {
    work_histories: -1,
    time_segments_per_day: -1,
    data_retention_days: -1,
    export_per_month: -1,
    fish_records_per_day: -1
  }
}

/**
 * 会员类型配置
 */
const MEMBERSHIP_TYPES = {
  free: {
    name: '免费用户',
    description: '基础功能，满足日常使用',
    icon: '👤',
    color: '#6B7280'
  },
  vip: {
    name: 'VIP会员',
    description: '全功能解锁，专业体验',
    icon: '👑',
    color: '#F59E0B'
  }
}

/**
 * 功能权限映射
 */
const FEATURE_PERMISSIONS = {
  // 数据同步
  data_sync: {
    name: '数据同步',
    description: '云端数据同步，多设备访问',
    vipOnly: true
  },
  
  // 高级分析
  advanced_analytics: {
    name: '高级分析',
    description: '详细的数据分析和趋势图表',
    vipOnly: true
  },
  
  // 数据导出
  export_data: {
    name: '数据导出',
    description: '导出工作数据为Excel文件',
    vipOnly: false
  },
  
  // 历史数据
  history_data: {
    name: '历史数据',
    description: '查看和管理历史工作数据',
    vipOnly: true
  },
  
  // 自定义主题
  custom_themes: {
    name: '自定义主题',
    description: '个性化界面主题设置',
    vipOnly: true
  }
}

/**
 * 获取免费功能列表
 * @returns {Array} 功能列表
 */
function getFreeFeatures() {
  return [...FREE_FEATURES]
}

/**
 * 获取VIP功能列表
 * @returns {Array} 功能列表
 */
function getVipFeatures() {
  return [...VIP_FEATURES]
}

/**
 * 获取使用限制
 * @param {string} membershipType - 会员类型 ('free' | 'vip')
 * @returns {Object} 限制配置
 */
function getUsageLimits(membershipType = 'free') {
  return JSON.parse(JSON.stringify(USAGE_LIMITS[membershipType] || USAGE_LIMITS.free))
}

/**
 * 检查功能权限
 * @param {string} featureName - 功能名称
 * @param {boolean} isVip - 是否为VIP用户
 * @returns {boolean} 是否有权限
 */
function hasFeaturePermission(featureName, isVip = false) {
  if (FREE_FEATURES.includes(featureName)) {
    return true
  }
  
  if (VIP_FEATURES.includes(featureName)) {
    return isVip
  }
  
  return false
}

/**
 * 检查使用限制
 * @param {string} limitType - 限制类型
 * @param {number} currentValue - 当前值
 * @param {boolean} isVip - 是否为VIP用户
 * @returns {boolean} 是否在限制内
 */
function checkUsageLimit(limitType, currentValue, isVip = false) {
  const limits = getUsageLimits(isVip ? 'vip' : 'free')
  const limit = limits[limitType]
  
  if (!limit || limit === -1) {
    return true // 无限制
  }
  
  return currentValue < limit
}

/**
 * 获取会员类型信息
 * @param {boolean} isVip - 是否为VIP用户
 * @returns {Object} 会员类型信息
 */
function getMembershipInfo(isVip = false) {
  const type = isVip ? 'vip' : 'free'
  return {
    ...MEMBERSHIP_TYPES[type],
    type: type,
    features: isVip ? [...FREE_FEATURES, ...VIP_FEATURES] : [...FREE_FEATURES],
    limits: getUsageLimits(type)
  }
}

/**
 * 获取功能权限信息
 * @param {string} featureName - 功能名称
 * @returns {Object|null} 功能权限信息
 */
function getFeaturePermissionInfo(featureName) {
  return FEATURE_PERMISSIONS[featureName] || null
}

module.exports = {
  FREE_FEATURES,
  VIP_FEATURES,
  USAGE_LIMITS,
  MEMBERSHIP_TYPES,
  FEATURE_PERMISSIONS,
  getFreeFeatures,
  getVipFeatures,
  getUsageLimits,
  hasFeaturePermission,
  checkUsageLimit,
  getMembershipInfo,
  getFeaturePermissionInfo
}
